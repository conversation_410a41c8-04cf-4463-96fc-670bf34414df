import api from '@/config/request'
import type { ResponseData } from '@/types/api/response-data.types'
import type { PrivilegeVerificationResponse } from '@/types/verification/privilege-verification-response.types'
import type { PrivilegeVerificationStartRequest } from '@/types/verification/privilege-verification-start-request.types'
import type { PrivilegeVerificationSubmitRequest } from '@/types/verification/privilege-verification-submit-request.types'
import type { VerificationTimeInfoResponse } from '@/types/verification/verification-time-info-response.types'

/**
 * 用户特权验证相关API接口
 * 提供特权验证流程的完整功能
 */
const verificationApi = {
  /** API基础路径 */
  URL: '/core/user-privilege-verifications',

  /**
   * 创建特权验证流程
   * @param request 验证启动请求参数
   * @returns 返回验证流程信息
   */
  startVerification: async (
    request: PrivilegeVerificationStartRequest,
  ): Promise<ResponseData<PrivilegeVerificationResponse>> => {
    const response = await api.post<ResponseData<PrivilegeVerificationResponse>>(
      verificationApi.URL,
      request,
    )
    return response.data
  },

  /**
   * 查询验证流程状态
   * @param id 验证流程ID
   * @returns 返回验证流程信息
   */
  verificationStatus: async (id: number): Promise<ResponseData<PrivilegeVerificationResponse>> => {
    const response = await api.get<ResponseData<PrivilegeVerificationResponse>>(
      `${verificationApi.URL}/${id}/status`,
    )
    return response.data
  },

  /**
   * 提交验证内容（步骤二）
   * @param id 验证流程ID
   * @param request 验证内容提交请求
   * @returns 返回提交结果
   */
  submitVerificationContent: async (
    id: number,
    request: PrivilegeVerificationSubmitRequest,
  ): Promise<ResponseData<boolean>> => {
    const response = await api.put<ResponseData<boolean>>(
      `${verificationApi.URL}/${id}/content`,
      request,
    )
    return response.data
  },

  /**
   * 触发步骤三（用户B访问验证页面时调用）
   * @param id 验证流程ID
   * @returns 返回触发结果
   */
  triggerStepThree: async (id: number): Promise<ResponseData<boolean>> => {
    const response = await api.put<ResponseData<boolean>>(`${verificationApi.URL}/${id}/step-three`)
    return response.data
  },

  /**
   * 启动验证计时器（用户点击邮件链接访问页面时调用）
   * @param id 验证流程ID
   * @returns 返回时间信息
   */
  startVerificationTimer: async (
    id: number,
  ): Promise<ResponseData<VerificationTimeInfoResponse>> => {
    const response = await api.post<ResponseData<VerificationTimeInfoResponse>>(
      `${verificationApi.URL}/${id}/timer`,
    )
    return response.data
  },

  /**
   * 获取验证剩余时间
   * @param id 验证流程ID
   * @returns 返回剩余时间信息
   */
  remainingTime: async (id: number): Promise<ResponseData<VerificationTimeInfoResponse>> => {
    const response = await api.get<ResponseData<VerificationTimeInfoResponse>>(
      `${verificationApi.URL}/${id}/remaining-time`,
    )
    return response.data
  },
}

export default verificationApi
