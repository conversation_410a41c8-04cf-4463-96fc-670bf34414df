/**
 * 验证流程状态常量
 */

/** 验证状态枚举 */
export const VERIFICATION_STATUS = {
  /** 进行中 */
  IN_PROGRESS: 0,
  /** 成功 */
  SUCCESS: 1,
  /** 失败 */
  FAILED: 2,
  /** 超时 */
  TIMEOUT: 3,
} as const

/** 验证状态标签映射 */
export const VERIFICATION_STATUS_LABELS = {
  [VERIFICATION_STATUS.IN_PROGRESS]: '进行中',
  [VERIFICATION_STATUS.SUCCESS]: '成功',
  [VERIFICATION_STATUS.FAILED]: '失败',
  [VERIFICATION_STATUS.TIMEOUT]: '超时',
} as const

/** 验证状态颜色映射 */
export const VERIFICATION_STATUS_COLORS = {
  [VERIFICATION_STATUS.IN_PROGRESS]: 'info',
  [VERIFICATION_STATUS.SUCCESS]: 'success',
  [VERIFICATION_STATUS.FAILED]: 'error',
  [VERIFICATION_STATUS.TIMEOUT]: 'warning',
} as const
