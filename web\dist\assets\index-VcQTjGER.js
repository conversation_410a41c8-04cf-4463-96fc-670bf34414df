const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ActivationCodeModal-5g0uANuV.js","assets/@vue-C18CzuYV.js","assets/vue-router-CM3lNrYT.js","assets/naive-ui-uw0PP3R0.js","assets/seemly-Cdi3gWV3.js","assets/vueuc-BKIYztcR.js","assets/evtd-hWw0KU7y.js","assets/@css-render-DcoOFqSU.js","assets/vooks-n2t0Q59N.js","assets/vdirs-Bvq7nEML.js","assets/@juggle-BnTvdTVm.js","assets/css-render-7x70jhNC.js","assets/@emotion-DFFAhID7.js","assets/lodash-es-BpE61GNB.js","assets/treemate-D3ikBJ7G.js","assets/date-fns-B2WPOLoy.js","assets/date-fns-tz-DYDqAHgp.js","assets/async-validator-Bed4cEOw.js","assets/vite-plugin-node-polyfills-CgB-Lgxu.js","assets/pinia-C1NaKXqp.js","assets/axios-CF6-kBsv.js","assets/@tiptap-BlbpWldy.js","assets/prosemirror-dropcursor-b1zulL1f.js","assets/prosemirror-state-Dg8eoqF5.js","assets/prosemirror-model-BwtArlLQ.js","assets/orderedmap-6uBVSRPO.js","assets/prosemirror-transform-2YHSeD6B.js","assets/prosemirror-view-D1p6KQzm.js","assets/prosemirror-gapcursor-De4VkbqI.js","assets/prosemirror-keymap-CxzETJ23.js","assets/w3c-keyname-DcELQ0J3.js","assets/prosemirror-history-B0l72feN.js","assets/rope-sequence-DIBo4VXF.js","assets/linkifyjs-CgqKPF1I.js","assets/tippy.js-Cq-jft6S.js","assets/@popperjs-B5AGR5A_.js","assets/prosemirror-commands-jNaZT4ky.js","assets/prosemirror-schema-list-DJ0wlwD0.js","assets/tiptap-markdown-C1mL59Et.js","assets/prosemirror-markdown-BKJFDS0M.js","assets/markdown-it-DV9r8h9J.js","assets/mdurl-DbZ9s47_.js","assets/uc.micro-CRGj88R_.js","assets/entities-D_unbCD7.js","assets/linkify-it-DVBmImI5.js","assets/punycode.js-H98b6B6Y.js","assets/markdown-it-task-lists-Dj-XbLVy.js","assets/highlight.js-O_OqoeFy.js","assets/smooth-scroll-into-view-if-needed-B9OyoO8F.js","assets/scroll-into-view-if-needed-CoacbxIo.js","assets/compute-scroll-into-view-Cfyw3hb3.js","assets/lowlight-CRhSxQ06.js","assets/sockjs-client-CnUIS0PG.js","assets/url-parse-BJt2elfP.js","assets/requires-port-CgMabaHb.js","assets/querystringify-B2QvdZsH.js","assets/inherits-BfZYsuMB.js","assets/@stomp-ba8ZO4qr.js","assets/ActivationCodeModal-CRBiMiD0.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,a,l)=>((t,a,l)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[a]=l)(t,"symbol"!=typeof a?a+"":a,l);import{d as a,c as l}from"./pinia-C1NaKXqp.js";import{r as n,d as i,t as o,o as r,f as s,n as c,X as u,Y as d,Z as m,_ as v,$ as p,u as g,a0 as h,a1 as f,a2 as w,H as y,a3 as b,K as k,U as x,a4 as C,F as L,a5 as S,v as T,c as M,h as R,a as A,q as E,W as _,a6 as I,G as z,a7 as P,N as B,B as $,A as D,w as U,T as V,a8 as F,y as O,J as H,a9 as j,S as N,x as q,z as Y,aa as W}from"./@vue-C18CzuYV.js";import{d as J,N as K,a as X,b as G,c as Z,e as Q,f as ee,g as te,S as ae,h as le,B as ne,i as ie,j as oe,k as re,l as se,m as ce,n as ue,o as de,p as me,q as ve,r as pe,s as ge,t as he,u as fe,v as we,w as ye,x as be,y as ke,z as xe,A as Ce,C as Le,D as Se,E as Te,F as Me,G as Re,H as Ae,I as Ee,J as _e,K as Ie,L as ze,M as Pe,O as Be,P as $e,Q as De,R as Ue,T as Ve,U as Fe,V as Oe,W as He,X as je,Y as Ne,Z as qe}from"./naive-ui-uw0PP3R0.js";import{u as Ye,a as We,c as Je,b as Ke}from"./vue-router-CM3lNrYT.js";import{a as Xe}from"./axios-CF6-kBsv.js";import{a as Ge,N as Ze,V as Qe,b as et,C as tt,E as at,I as lt,c as nt,S as it,T as ot,D as rt,P as st,d as ct,e as ut,B as dt,f as mt,h as vt,U as pt,j as gt,H as ht,k as ft,O as wt,L as yt,l as bt,m as kt,n as xt,o as Ct,p as Lt,q as St,r as Tt,s as Mt,t as Rt,F as At,G as Et,u as _t,v as It,w as zt,x as Pt,y as Bt,z as $t,A as Dt,J as Ut}from"./@tiptap-BlbpWldy.js";import{M as Vt}from"./tiptap-markdown-C1mL59Et.js";import{S as Ft,a as Ot,P as Ht}from"./prosemirror-state-Dg8eoqF5.js";import{s as jt}from"./smooth-scroll-into-view-if-needed-B9OyoO8F.js";import{a as Nt,D as qt}from"./prosemirror-view-D1p6KQzm.js";import{c as Yt,g as Wt}from"./lowlight-CRhSxQ06.js";import{S as Jt}from"./sockjs-client-CnUIS0PG.js";import{C as Kt}from"./@stomp-ba8ZO4qr.js";import"./seemly-Cdi3gWV3.js";import"./vueuc-BKIYztcR.js";import"./evtd-hWw0KU7y.js";import"./@css-render-DcoOFqSU.js";import"./vooks-n2t0Q59N.js";import"./vdirs-Bvq7nEML.js";import"./@juggle-BnTvdTVm.js";import"./css-render-7x70jhNC.js";import"./@emotion-DFFAhID7.js";import"./lodash-es-BpE61GNB.js";import"./treemate-D3ikBJ7G.js";import"./date-fns-B2WPOLoy.js";import"./date-fns-tz-DYDqAHgp.js";import"./async-validator-Bed4cEOw.js";import"./vite-plugin-node-polyfills-CgB-Lgxu.js";import"./prosemirror-dropcursor-b1zulL1f.js";import"./prosemirror-transform-2YHSeD6B.js";import"./prosemirror-model-BwtArlLQ.js";import"./orderedmap-6uBVSRPO.js";import"./prosemirror-gapcursor-De4VkbqI.js";import"./prosemirror-keymap-CxzETJ23.js";import"./w3c-keyname-DcELQ0J3.js";import"./prosemirror-history-B0l72feN.js";import"./rope-sequence-DIBo4VXF.js";import"./linkifyjs-CgqKPF1I.js";import"./tippy.js-Cq-jft6S.js";import"./@popperjs-B5AGR5A_.js";import"./prosemirror-commands-jNaZT4ky.js";import"./prosemirror-schema-list-DJ0wlwD0.js";import"./prosemirror-markdown-BKJFDS0M.js";import"./markdown-it-DV9r8h9J.js";import"./mdurl-DbZ9s47_.js";import"./uc.micro-CRGj88R_.js";import"./entities-D_unbCD7.js";import"./linkify-it-DVBmImI5.js";import"./punycode.js-H98b6B6Y.js";import"./markdown-it-task-lists-Dj-XbLVy.js";import"./highlight.js-O_OqoeFy.js";import"./scroll-into-view-if-needed-CoacbxIo.js";import"./compute-scroll-into-view-Cfyw3hb3.js";import"./url-parse-BJt2elfP.js";import"./requires-port-CgMabaHb.js";import"./querystringify-B2QvdZsH.js";import"./inherits-BfZYsuMB.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const a of e)if("childList"===a.type)for(const e of a.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const Xt="localhost"==window.location.hostname?`${window.location.protocol}//localhost:20001`:`${window.location.protocol}//${window.location.host}/api`,Gt={logLevel:"error",turnstile:{siteKey:"0x4AAAAAABk65WObKzE7_dzc"},backend:{baseURL:Xt,wsURL:`${Xt}/core/ws`,resourceURL:`${Xt}/osr`}},Zt={DEBUG:"debug",INFO:"info",WARN:"warn",ERROR:"error"},Qt=Gt.logLevel;function ea(e){const t=Object.values(Zt),a=t.indexOf(Qt);return t.indexOf(e)>=a}const ta={levels:Zt,debug(e,...t){ea(Zt.DEBUG)},info(e,...t){ea(Zt.INFO)},warn(e,...t){ea(Zt.WARN)},error(e,...t){ea(Zt.ERROR)}};var aa=(e=>(e.LIGHT="light",e.DARK="dark",e))(aa||{});const la="loginUser";class na{static parse(e){if("string"!=typeof e)return ta.warn("Non-string input to JSON.parse:",typeof e),null;if(!e||""===e.trim())return ta.warn("Empty string passed to JSON.parse"),null;if(e.includes("@")){ta.debug("Detected @ symbol in string, trying to sanitize");try{const t=/@([^:]+)\s*:\s*(\{.*\})/,a=e.match(t);if(a&&a[2]){const e=a[2];return JSON.parse(e)}const l=e.indexOf("{"),n=e.lastIndexOf("}");if(-1!==l&&-1!==n&&n>l){const t=e.substring(l,n+1);return JSON.parse(t)}}catch(t){ta.debug("Failed to extract JSON from string with @ symbol:",t)}}try{return JSON.parse(e)}catch(t){return ta.warn("Invalid JSON format:",t instanceof Error?t.message:"Unknown error"),ta.debug("Problem JSON string:",e.length>100?e.substring(0,97)+"...":e),null}}static stringify(e,t=!1){try{return t?JSON.stringify(e,null,2):JSON.stringify(e)}catch(a){return ta.error("JSON stringify error:",a),""}}static deepClone(e){return JSON.parse(JSON.stringify(e))}static isValidJson(e){try{return JSON.parse(e),!0}catch{return!1}}static getProperty(e,t){return t.split(".").reduce(((e,t)=>{if(e&&"object"==typeof e&&!Array.isArray(e)&&t in e)return e[t]}),e)}static setProperty(e,t,a){const l=t.split(".");let n=e;l.forEach(((e,t)=>{if(t===l.length-1)n[e]=a;else{n[e]||(n[e]={});const t=n[e];"object"!=typeof t||Array.isArray(t)||null===t||(n=t)}}))}}class ia{static set(e,t){const a=na.stringify(t);localStorage.setItem(e,a)}static get(e){const t=localStorage.getItem(e);return t?na.parse(t):null}static remove(e){localStorage.removeItem(e)}static clear(){localStorage.clear()}static getLoginUser(){const e=this.get(la);return e?function(e){return{id:e.id,username:e.username,phone:e.phone,email:e.email,avatar:e.avatar,ipLocation:e.ipLocation,job:e.job,level:e.level,notificationReceiveType:e.notificationReceiveType,config:e.config}}(e):null}static setLoginUser(e){const t=(a=e,{...a});var a;this.set(la,t)}static removeLoginUser(){this.remove(la)}}const oa={white:"#fff","white-1":"#f0f0f0","white-2":"#ddd","creamy-white-1":"#eeece4","creamy-white-2":"#e4e1d8","creamy-white-3":"#dcd8ca",black:"#2e2b29","black-contrast":"#110f0e","dark-gray":"#191919","dark-gray-1":"#202020","deep-gray":"#111","gray-1":"rgba(61, 37, 20, 0.05)","gray-2":"rgba(61, 37, 20, 0.08)","gray-3":"rgba(61, 37, 20, 0.12)","gray-4":"rgba(53, 38, 28, 0.3)","gray-5":"rgba(28, 25, 23, 0.6)",green:"#22c55e",blue:"#4ba3fd","blue-light":"#e6f3ff",purple:"#6a00f5","purple-contrast":"#5800cc","purple-light":"rgba(88, 5, 255, 0.05)","yellow-contrast":"#facc15",yellow:"rgba(250, 204, 21, 0.4)","yellow-light":"#fffae5",red:"#ff5c33","red-light":"#ffebe5","border-1":"0.1rem solid rgba(61, 37, 20, 0.12)",shadow:"0 0.25rem 0.6rem rgba(0, 0, 0, 0.1)","code-text":"#24292e","code-comment":"#6a737d","code-keyword":"#d73a49","code-string":"#032f62","code-number":"#005cc5","code-function":"#6f42c1","code-variable":"#e36209","code-tag":"#22863a","code-attribute":"#6f42c1","code-builtin":"#005cc5","code-meta":"#6a737d","code-deletion-color":"#b31d28","code-deletion-bg":"#ffeef0","code-addition-color":"#22863a","code-addition-bg":"#f0fff4"},ra={white:"#121212","white-1":"#242424","white-2":"#363636","creamy-white-1":"#1a1a1a","creamy-white-2":"#262626","creamy-white-3":"#333",black:"#e0e0e0","black-contrast":"#fff","dark-gray":"#191919","dark-gray-1":"#202020","deep-gray":"#111","gray-1":"rgba(200, 200, 200, 0.05)","gray-2":"rgba(200, 200, 200, 0.08)","gray-3":"rgba(200, 200, 200, 0.12)","gray-4":"rgba(200, 200, 200, 0.3)","gray-5":"rgba(200, 200, 200, 0.6)",green:"#22c55e",blue:"#4ba3fd","blue-light":"#2a3745",purple:"#9d6dff","purple-contrast":"#8a5cf5","purple-light":"rgba(154, 92, 255, 0.15)","yellow-contrast":"#facc15",yellow:"rgba(250, 204, 21, 0.4)","yellow-light":"#3f3a14",red:"#ff5c33","red-light":"#3d1a12","border-1":"0.1rem solid rgba(200, 200, 200, 0.12)",shadow:"0 0.25rem 0.6rem rgba(255, 255, 255, 0.1)","code-text":"#e6edf3","code-comment":"#8b949e","code-keyword":"#ff7b72","code-string":"#a5d6ff","code-number":"#79c0ff","code-function":"#d2a8ff","code-variable":"#ffa657","code-tag":"#7ee787","code-attribute":"#d2a8ff","code-builtin":"#79c0ff","code-meta":"#8b949e","code-deletion-color":"#ffa198","code-deletion-bg":"#490202","code-addition-color":"#56d364","code-addition-bg":"#0f5132"};function sa(e){const t=document.documentElement,a=function(e){const t={};for(const[a,l]of Object.entries(e))t[`--${a}`]=l;return t}(e);for(const[l,n]of Object.entries(a))t.style.setProperty(l,n)}const ca="wen-theme";aa.LIGHT,aa.DARK;const ua={[aa.LIGHT]:null,[aa.DARK]:J},da=n(aa.LIGHT),ma=n(!1),va=()=>{const e=da.value===aa.LIGHT?aa.DARK:aa.LIGHT;return ma.value=!0,da.value=e,ia.set(ca,e),pa(e),ga(),setTimeout((()=>{ma.value=!1}),50),e},pa=e=>{const t=document.documentElement,a=e===aa.DARK,l=function(e){return e?ra:oa}(a);sa(l),t.setAttribute("data-theme",e),a?t.classList.add("dark-theme"):t.classList.remove("dark-theme")},ga=e=>{[".search-container",".n-input",".user-info-group",".user-info",".avatar-container",".n-popover",".notification-container",".comment-info-container",".comment-list-container",".user-comment-container",".user-comment-container-fixed",".comment-content-row",".comment-input-row",".comment-reply-row",".tiptap-editor-wrapper",".editor-content",".ProseMirror",".ProseMirrorInput",".article-content"].forEach((e=>{document.querySelectorAll(e).forEach((e=>{e instanceof HTMLElement&&e.classList.add("theme-priority")}))})),document.querySelectorAll(".ProseMirror, .editor-content, .tiptap-editor-wrapper").forEach((e=>{e instanceof HTMLElement&&(e.setAttribute("data-theme-refresh","true"),requestAnimationFrame((()=>{e.offsetHeight,setTimeout((()=>e.removeAttribute("data-theme-refresh")),50)})))}));document.querySelectorAll(".comment-info-container, .article-info-container").forEach((e=>{if(e instanceof HTMLElement){const t=e.style.display;e.style.display="none",e.offsetHeight,e.style.display=t}}))},ha=i({__name:"App",setup(e){const t=o((()=>ua[da.value]));return r((()=>{(()=>{const e=ia.get(ca);e&&Object.values(aa).includes(e)&&(da.value=e,pa(e))})()})),s(da,(e=>{c((()=>{[".search-container",".n-input",".user-info-group",".user-info",".avatar-container",".comment-info-container",".comment-list-container",".user-comment-container",".user-comment-container-fixed",".comment-content-row",".comment-input-row",".comment-reply-row",".tiptap-editor-wrapper",".editor-content",".ProseMirror",".ProseMirrorInput"].forEach((e=>{document.querySelectorAll(e).forEach((e=>{e instanceof HTMLElement&&e.classList.add("theme-priority")}))})),document.querySelectorAll(".ProseMirror, .editor-content").forEach((e=>{e instanceof HTMLElement&&(e.setAttribute("data-force-update",Date.now().toString()),setTimeout((()=>e.removeAttribute("data-force-update")),10))})),ta.debug(`Theme changed to: ${e}`)}))})),(e,a)=>{const l=u("router-view");return m(),d(g(Q),{theme:t.value},{default:v((()=>[p(g(K),null,{default:v((()=>[p(g(X),null,{default:v((()=>[p(g(G),null,{default:v((()=>[p(g(Z),null,{default:v((()=>[p(l)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["theme"])}}}),fa="request",wa={},ya={},ba=(e,t,a=300)=>{wa[e]&&clearTimeout(wa[e]),wa[e]=setTimeout((()=>{t()}),a)},ka=(e,t,a=1e3)=>{const l=Date.now();(!ya[e]||l-ya[e]>=a)&&(t(),ya[e]=l)},{message:xa}=ee(["message"]),Ca=Xe.create({baseURL:Gt.backend.baseURL,timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});Ca.interceptors.request.use(),Ca.interceptors.response.use((e=>e),(e=>{const{response:t}=e;if(!t)return ba(fa+"net",(()=>{xa.warning("网络错误，请稍后重试")})),Promise.reject(null);{const e=t.status;if(401===e)return ia.removeLoginUser(),ba(fa+401,(()=>{xa.warning("登录已过期，请重新登录")})),uu.push("/login"),document.cookie.split(";").forEach((function(e){const t=e.split("=")[0].trim();document.cookie=`${t}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`})),Promise.reject(null);if(403===e)return xa.warning(t.data.message),Promise.reject(null)}return Promise.reject(e)}));const La=(e,t,a)=>Ca.get(e,{...a,params:t}),Sa=(e,t)=>Ca.post(e,t),Ta=(e,t,a)=>Ca.post(e,t,a),Ma=(e,t)=>Ca.put(e,t),Ra=(e,t,a)=>Ca.put(e,t,a),Aa=(e,t)=>Ca.patch(e,t),Ea=(e,t)=>Ca.delete(e,{params:t}),_a={URL:"/core/files",imageTypes:["image/png","image/jpg","image/jpeg","image/gif","image/webp"],upload:async(e,t)=>{const a=new FormData;a.append("bucket",t),a.append("file",e);return(await Ta(_a.URL,a,{headers:{"Content-Type":"multipart/form-data"}})).data},uploadImage:async(e,t)=>_a.imageTypes.includes(e.type)?_a.upload(e,t):Promise.reject(new Error("文件类型不支持，请上传图片文件")),getResourceURL:e=>e?e.startsWith("http")?e:`${Gt.backend.resourceURL}${e}`:""},Ia="/thumbnail";const za=e=>e.replace(/\/+/g,"/").replace(/^\/+/,"/"),Pa=e=>{if(!e)return"";if(!e.startsWith("http"))return za(e);if(e.includes("localhost")){const t=e.match(/\/osr(\/.*$)/);if(t&&t[1])return za(t[1])}if(!/^https?:\/\/[^\s]+$/.test(e)){ta.warn("Invalid URL format:",e);const t=e.match(/\/[^?#]*/);return za(t?t[0]:e)}const t=new URL(e).pathname;return t.startsWith("/")?za(t.substring(1)):za(t)},Ba=(e,t=!1)=>{const a=Pa(e),l=t?a:a.replace(Ia,"");return _a.getResourceURL(l)},$a=(e,t,a,l=!1)=>(ta.debug("handleImageUpload started for file:",e.name,e.type),_a.uploadImage(e,a).then((e=>{var a,n;let i=e.data;ta.debug("Original image URL from server:",i),!l&&i.includes(Ia)&&(i=i.replace(Ia,""),ta.debug("Removed thumbnail from URL:",i));const o=(null==(a=t.storage.image)?void 0:a.transformSrc)?t.storage.image.transformSrc(i):Pa(i);ta.debug("Converted to relative path:",o);const r=document.createElement("img");r.onerror=e=>{ta.error("Image loading error:",e)},r.onload=()=>{let e=r.width,a=r.height,l=1;if(ta.debug("Original image dimensions:",{width:e,height:a,aspectRatio:e/a}),e>600||a>800){const t=600/e,n=800/a;l=Math.min(t,n),e=Math.round(e*l),a=Math.round(a*l)}ta.debug("Adjusted image dimensions:",{width:e,height:a,ratio:l}),ta.debug("Setting image with relative path:",o);const n={src:o,width:`${e}px`,height:`${a}px`};t.commands.setImage(n)};const s=(null==(n=t.storage.image)?void 0:n.getFullUrl)?t.storage.image.getFullUrl(i):Ba(i,l);return ta.debug("Loading image with full URL:",s),r.src=s,o}))),Da={replaceImageUrls:(e,t=!1)=>{var a;null==(a=null==e?void 0:e.content)||a.forEach((e=>{"image"===e.type&&e.attrs&&e.attrs.src&&(e.attrs.src=t?Ba(e.attrs.src,!1):Pa(e.attrs.src)),e.content&&Da.replaceImageUrls(e,t)}))},toJsonString:e=>{const t=JSON.parse(JSON.stringify(e)),a=e=>{if(e){if("image"===e.type&&e.attrs&&e.attrs.src){const t=e.attrs.src;(()=>{if(t.includes("/thumbnail"))return!1;const e=document.querySelectorAll("img[data-relative-src]");for(const a of e){const e=a.dataset.relativeSrc;if(e&&e.includes("/thumbnail")&&e.replace("/thumbnail","")===t)return!0}return!1})()?(e.attrs.src="/thumbnail"+t,ta.debug("Restored thumbnail path:",{original:t,thumbnail:e.attrs.src})):(e.attrs.src=Pa(t),ta.debug("Image URL transformed:",{original:t,transformed:e.attrs.src}))}e.content&&Array.isArray(e.content)&&e.content.forEach(a)}};return a(t),na.stringify(t)},toJsonObject:e=>{if(!e)return ta.warn("Empty content passed to toJsonObject"),{type:"doc",content:[{type:"paragraph",content:[]}]};try{if("object"==typeof e)return e;const t=na.parse(e);return t||(ta.warn("Failed to parse content in toJsonObject, creating fallback structure"),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"无法显示内容"}]}]})}catch(t){return ta.error("Error in toJsonObject:",t),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"解析错误"}]}]}}},serializeContent:e=>{let t="";if(!e)return t;return function e(a){var l;"text"===a.type?t+=a.text:"mention"===a.type?t+="@"+(null==(l=a.attrs)?void 0:l.label):"image"===a.type?t+="[图片]":a.content&&Array.isArray(a.content)&&a.content.forEach((t=>e(t)))}(e),t}},Ua={class:"bilibili-wrapper"},Va=["src","data-danmaku","data-mute"],Fa={key:1,class:"bilibili-error"},Oa=(e,t)=>{const a=e.__vccOpts||e;for(const[l,n]of t)a[l]=n;return a},Ha=Oa(i({__name:"BilibiliNodeView",props:{node:{},decorations:{},selected:{type:Boolean},updateAttributes:{type:Function},deleteNode:{type:Function},view:{},getPos:{type:Function},innerDecorations:{},editor:{},extension:{},HTMLAttributes:{}},setup(e){const t=e,a=o((()=>{const e=(e=>{if(!e)return null;const t=e.match(/(BV[a-zA-Z0-9]{10})/),a=e.match(/aid=(\d+)/),l=e.match(/b23\.tv\/([a-zA-Z0-9]+)/),n=e.match(/bilibili\.com\/video\/([a-zA-Z0-9]+)/);return t?t[1]:a?a[1]:l?l[1]:n?n[1]:null})(t.node.attrs.src);if(!e)return null;return`//www.bilibili.com/blackboard/html5mobileplayer.html?${new URLSearchParams({[e.startsWith("BV")?"bvid":"aid"]:e,page:"1",danmaku:t.node.attrs.danmaku?"1":"0",mute:t.node.attrs.mute?"1":"0",high_quality:"1"}).toString()}`}));return(e,t)=>(m(),d(g(Ge),{class:"bilibili-container"},{default:v((()=>[h("div",Ua,[a.value?(m(),f("iframe",{key:0,src:a.value,scrolling:"no",border:"0",frameborder:"0",framespacing:"0",allowfullscreen:"true","data-danmaku":e.node.attrs.danmaku,"data-mute":e.node.attrs.mute,class:"bilibili-iframe"},null,8,Va)):(m(),f("div",Fa,t[0]||(t[0]=[h("p",null,"无效的 Bilibili 视频链接",-1)])))])])),_:1}))}}),[["__scopeId","data-v-65da1327"]]),ja=e=>{const t=e.match(/(BV[a-zA-Z0-9]{10})/),a=e.match(/aid=(\d+)/),l=e.match(/b23\.tv\/([a-zA-Z0-9]+)/),n=e.match(/bilibili\.com\/video\/([a-zA-Z0-9]+)/);return t?t[1]:a?a[1]:l?l[1]:n?n[1]:null},Na=Ze.create({name:"bilibili",group:"block",content:"text*",inline:!1,atom:!0,addAttributes:()=>({src:{default:null,parseHTML:e=>{const t=e.getAttribute("src");return t?ja(t):null}},danmaku:{default:!0},mute:{default:!1}}),parseHTML:()=>[{tag:"iframe[src]",getAttrs:e=>({src:e.getAttribute("src"),danmaku:"true"===e.getAttribute("data-danmaku"),mute:"true"===e.getAttribute("data-mute")})}],renderHTML({node:e}){const{src:t,danmaku:a,mute:l}=e.attrs,n=ja(t);if(!n)return["div",{class:"bilibili-error"},"无效的 Bilibili 视频链接"];return["div",{class:"bilibili-container"},["div",{class:"bilibili-wrapper"},["iframe",{src:`//www.bilibili.com/blackboard/html5mobileplayer.html?${new URLSearchParams({[n.startsWith("BV")?"bvid":"aid"]:n,page:"1",danmaku:a?"1":"0",mute:l?"1":"0",high_quality:"1"}).toString()}`,scrolling:"no",border:"0",frameborder:"0",framespacing:"0",allowfullscreen:"true","data-danmaku":a,"data-mute":l,class:"bilibili-iframe"}]]]},addNodeView:()=>Qe(Ha),addCommands(){return{setBilibiliVideo:e=>({commands:t})=>t.insertContent({type:this.name,attrs:e})}}}),qa={class:"code-block-header"},Ya={class:"code-block-language-container"},Wa={key:1,class:"code-block-language",contenteditable:"false",style:{userSelect:"none",pointerEvents:"none"}},Ja={class:"code-block-toolbar"},Ka=["title"],Xa=["title"],Ga=Oa(i({__name:"CodeBlockToolbar",props:{language:{},wrapMode:{type:Boolean},copyState:{},isEditable:{type:Boolean}},emits:["toggle-wrap","copy-code","language-change"],setup(e,{emit:t}){const a=t,l=[{label:"Plain Text",value:"text"},{label:"JavaScript",value:"javascript"},{label:"TypeScript",value:"typescript"},{label:"HTML",value:"html"},{label:"CSS",value:"css"},{label:"SCSS",value:"scss"},{label:"Vue",value:"vue"},{label:"Java",value:"java"},{label:"Python",value:"python"},{label:"C++",value:"cpp"},{label:"C",value:"c"},{label:"C#",value:"csharp"},{label:"Go",value:"go"},{label:"Rust",value:"rust"},{label:"PHP",value:"php"},{label:"Ruby",value:"ruby"},{label:"Swift",value:"swift"},{label:"Kotlin",value:"kotlin"},{label:"Dart",value:"dart"},{label:"Shell",value:"bash"},{label:"PowerShell",value:"powershell"},{label:"SQL",value:"sql"},{label:"JSON",value:"json"},{label:"XML",value:"xml"},{label:"YAML",value:"yaml"},{label:"Markdown",value:"markdown"},{label:"Dockerfile",value:"dockerfile"},{label:"Nginx",value:"nginx"},{label:"Apache",value:"apache"}],n=e=>{a("language-change",e)};return(e,t)=>(m(),f("div",qa,[h("div",Ya,[e.isEditable?(m(),d(g(te),{key:0,value:e.language,options:l,size:"small",class:"code-language-select","consistent-menu-width":!1,"onUpdate:value":n},null,8,["value"])):(m(),f("span",Wa,w(e.language),1))]),h("div",Ja,[h("button",{class:y(["code-wrap-button",{active:e.wrapMode}]),title:e.wrapMode?"禁用换行":"启用换行",onClick:t[0]||(t[0]=t=>e.$emit("toggle-wrap"))},t[2]||(t[2]=[b('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-ec32a7e7><polyline points="17 2 21 6 17 10" data-v-ec32a7e7></polyline><path d="M3 11V9a4 4 0 0 1 4-4h14" data-v-ec32a7e7></path><polyline points="7 22 3 18 7 14" data-v-ec32a7e7></polyline><path d="M21 13v2a4 4 0 0 1-4 4H3" data-v-ec32a7e7></path></svg>',1)]),10,Ka),h("button",{class:y(["code-copy-button",{active:e.copyState.copied}]),title:e.copyState.copied?"已复制":"复制代码",onClick:t[1]||(t[1]=t=>e.$emit("copy-code"))},t[3]||(t[3]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[h("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),h("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})],-1)]),10,Xa)])]))}}),[["__scopeId","data-v-ec32a7e7"]]),Za=Oa(i({__name:"CodeBlockNodeView",props:{decorations:{},selected:{type:Boolean},updateAttributes:{type:Function},deleteNode:{type:Function},node:{},view:{},getPos:{type:Function},innerDecorations:{},editor:{},extension:{},HTMLAttributes:{}},setup(e){const t=e,a=n(!1),l=n({copied:!1,timer:null}),i=o((()=>t.editor.isEditable)),s=o((()=>{const e=t.node.attrs.language||"text";return"null"===e?"text":e})),c=o((()=>({display:"block",padding:"0.8rem 1rem",margin:"0",background:"transparent",border:"none",borderRadius:"0",fontFamily:"inherit",fontSize:"inherit",lineHeight:"inherit",whiteSpace:a.value?"pre-wrap":"pre",wordBreak:a.value?"break-word":"normal",overflowWrap:a.value?"break-word":"normal",width:"100%",boxSizing:"border-box"}))),u=()=>{a.value=!a.value},f=async()=>{try{const e=t.node.textContent||"";await navigator.clipboard.writeText(e),xa.success("代码已复制到剪贴板"),l.value.copied=!0,ba("tiptap-code-copy",(()=>{l.value.copied=!1}),3e3)}catch(e){xa.error("复制失败，请手动复制")}},w=e=>{t.updateAttributes&&t.updateAttributes({language:e})};return r((()=>{void 0!==t.node.attrs.wrap&&null!==t.node.attrs.wrap&&(a.value=Boolean(t.node.attrs.wrap))})),(e,t)=>(m(),d(g(Ge),{as:"pre",class:y(["code-block-container",{"code-wrap":a.value,"code-block-readonly":!i.value,"editable-mode":i.value,"readonly-mode":!i.value}]),"data-language":s.value,"data-selectable":!1},{default:v((()=>[p(Ga,{language:s.value,"wrap-mode":a.value,"copy-state":l.value,"is-editable":i.value,onToggleWrap:u,onCopyCode:f,onLanguageChange:w},null,8,["language","wrap-mode","copy-state","is-editable"]),h("div",{class:y(["code-scrollbar-container",{"code-wrap":a.value}])},[p(g(ae),{"x-scrollable":!a.value,"y-scrollable":!1,trigger:"hover",size:8},{default:v((()=>[p(g(et),{as:"code",class:y([`language-${s.value}`,{"code-wrap-enabled":a.value,"code-selectable":!i.value}]),style:k(c.value)},null,8,["class","style"])])),_:1},8,["x-scrollable"])],2)])),_:1},8,["class","data-language"]))}}),[["__scopeId","data-v-2743c4fd"]]),Qa=tt.extend({selectable:!1,atom:!1,draggable:!1,addOptions(){var e;return{...null==(e=this.parent)?void 0:e.call(this),HTMLAttributes:{class:"code-block-readonly","data-selectable":"false"},exitOnTripleEnter:!1,exitOnArrowDown:!1}},addNodeView:()=>Qe(Za),addKeyboardShortcuts(){var e;return{...(null==(e=this.parent)?void 0:e.call(this))||{},Enter:({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:l,empty:n}=a;return!(!n||l.parent.type!==this.type)&&e.commands.first((({commands:e})=>[()=>e.newlineInCode()]))},"Shift-Enter":({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:l,empty:n}=a;if(!n||l.parent.type!==this.type)return!1;const i=l.before();return e.chain().insertContentAt(i,{type:"paragraph"}).command((({tr:e})=>{const t=i;return e.setSelection(Ft.near(e.doc.resolve(t))),!0})).run()},"Ctrl-Enter":({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:l,empty:n}=a;if(!n||l.parent.type!==this.type)return!1;const i=l.after();return e.chain().insertContentAt(i,{type:"paragraph"}).command((({tr:e})=>{const t=i+1;return e.setSelection(Ft.near(e.doc.resolve(t))),!0})).run()}}}}),el=at.create({name:"fullscreen"}),tl={passive:!0},al=new Set(["wheel","touchstart","touchmove","touchend","touchcancel","scroll"]),ll=new Set(["wheel"]);function nl(e,t){return function(e,t,a,l={},n=!1){const i=!n&&al.has(t),o={...tl,...l,passive:i};(n||ll.has(t))&&(o.passive=!1);try{e.addEventListener(t,a,o)}catch(r){e.addEventListener(t,a,o.passive)}}(e,"wheel",t,{},!0),()=>{!function(e,t,a,l={}){try{e.removeEventListener(t,a,l)}catch(n){}}(e,"wheel",t)}}function il(e,t,a){const l=nl(e,a),n=a=>{a.target===e&&t()};e.addEventListener("click",n);const i=e=>{"Escape"===e.key&&t()};return document.addEventListener("keydown",i),()=>{l(),e.removeEventListener("click",n),document.removeEventListener("keydown",i)}}const ol={minScale:.5,maxScale:3};function rl(e,t){const a=e.clientX-t.clientX,l=e.clientY-t.clientY;return Math.sqrt(a*a+l*l)}function sl(e,t){return{x:(e.clientX+t.clientX)/2,y:(e.clientY+t.clientY)/2}}function cl(e,t={},a,l){const n=function(e={}){const{minScale:t=ol.minScale,maxScale:a=ol.maxScale}=e;return{state:{scale:1,translateX:0,translateY:0,isDragging:!1,isZooming:!1},dragVars:{dragStartX:0,dragStartY:0,startTranslateX:0,startTranslateY:0,hasDragged:!1,dragThreshold:5},scaleVars:{isScaling:!1,scaleStartDistance:0,scaleStartScale:1,scaleCenterX:0,scaleCenterY:0},performanceVars:{animationFrameId:null,pendingTransform:!1},options:{minScale:t,maxScale:a}}}(t),i=function(e,t){const{state:a,performanceVars:l,options:n}=t,{minScale:i,maxScale:o}=n,r=()=>{l.pendingTransform||(l.pendingTransform=!0,l.animationFrameId=requestAnimationFrame((()=>{e.style.transform=`translate3d(${a.translateX}px, ${a.translateY}px, 0) scale(${a.scale})`,l.pendingTransform=!1})))};return{updateTransform:r,resetTransform:()=>{l.animationFrameId&&(cancelAnimationFrame(l.animationFrameId),l.animationFrameId=null),a.scale=1,a.translateX=0,a.translateY=0,a.isDragging=!1,a.isZooming=!1,t.scaleVars.isScaling=!1,l.pendingTransform=!1,e.style.transform="",e.style.cursor="",e.style.willChange="",e.classList.remove("dragging","zooming")},handleWheelZoom:t=>{t.preventDefault();const l=t.deltaY>0?.9:1.1,n=Math.max(i,Math.min(o,a.scale*l));if(Math.abs(n-a.scale)>.01){const l=e.getBoundingClientRect(),i=t.clientX-l.left,o=t.clientY-l.top,s=(i-l.width/2-a.translateX)/a.scale,c=(o-l.height/2-a.translateY)/a.scale;a.scale=n,a.translateX=i-l.width/2-s*a.scale,a.translateY=o-l.height/2-c*a.scale,r()}}}}(e,n),o=function(e,t,a,l){const{state:n,dragVars:i}=t,{updateTransform:o}=a,r=e=>{0===e.button&&(e.preventDefault(),e.stopPropagation(),n.isDragging=!0,i.hasDragged=!1,i.dragStartX=e.clientX,i.dragStartY=e.clientY,i.startTranslateX=n.translateX,i.startTranslateY=n.translateY,document.addEventListener("mousemove",s,{passive:!1}),document.addEventListener("mouseup",c,{passive:!0}))},s=t=>{if(!n.isDragging)return;t.preventDefault();const a=t.clientX-i.dragStartX,l=t.clientY-i.dragStartY;!i.hasDragged&&(Math.abs(a)>i.dragThreshold||Math.abs(l)>i.dragThreshold)&&(i.hasDragged=!0,e.style.cursor="grabbing",e.classList.add("dragging")),i.hasDragged&&(n.translateX=i.startTranslateX+a,n.translateY=i.startTranslateY+l,o())},c=()=>{const t=!i.hasDragged;n.isDragging=!1,e.style.cursor="grab",e.classList.remove("dragging"),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",c),t&&l&&l()};return{handleMouseDown:r,handleMouseMove:s,handleMouseUp:c,addMouseEventListeners:()=>{e.addEventListener("mousedown",r,{passive:!1}),e.style.cursor="grab"},removeMouseEventListeners:()=>{e.removeEventListener("mousedown",r),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",c)}}}(e,n,i,a),r=function(e,t,a,l){const{state:n,dragVars:i,scaleVars:o,options:r}=t,{updateTransform:s}=a,{minScale:c,maxScale:u}=r,d=t=>{if(1===t.touches.length){t.preventDefault(),n.isDragging=!0,i.hasDragged=!1;const e=t.touches[0];i.dragStartX=e.clientX,i.dragStartY=e.clientY,i.startTranslateX=n.translateX,i.startTranslateY=n.translateY}else if(2===t.touches.length){t.preventDefault(),n.isDragging=!1,n.isZooming=!0,o.isScaling=!0,o.scaleStartDistance=rl(t.touches[0],t.touches[1]),o.scaleStartScale=n.scale;const a=sl(t.touches[0],t.touches[1]),l=e.getBoundingClientRect(),i=a.x-l.left,r=a.y-l.top;o.scaleCenterX=(i-l.width/2-n.translateX)/n.scale,o.scaleCenterY=(r-l.height/2-n.translateY)/n.scale,e.classList.remove("dragging"),e.classList.add("zooming")}},m=t=>{if(2===t.touches.length){t.preventDefault(),n.isDragging=!1,n.isZooming=!0,o.isScaling=!0,o.scaleStartDistance=rl(t.touches[0],t.touches[1]),o.scaleStartScale=n.scale;const a=sl(t.touches[0],t.touches[1]),l=e.getBoundingClientRect(),i=a.x-l.left,r=a.y-l.top;o.scaleCenterX=(i-l.width/2-n.translateX)/n.scale,o.scaleCenterY=(r-l.height/2-n.translateY)/n.scale,e.classList.remove("dragging"),e.classList.add("zooming")}},v=t=>{if(1===t.touches.length&&n.isDragging&&!n.isZooming){t.preventDefault();const a=t.touches[0],l=a.clientX-i.dragStartX,o=a.clientY-i.dragStartY;!i.hasDragged&&(Math.abs(l)>i.dragThreshold||Math.abs(o)>i.dragThreshold)&&(i.hasDragged=!0,e.classList.add("dragging")),i.hasDragged&&(n.translateX=i.startTranslateX+l,n.translateY=i.startTranslateY+o,s())}else if(2===t.touches.length&&n.isZooming&&o.isScaling){t.preventDefault();const a=rl(t.touches[0],t.touches[1])/o.scaleStartDistance;let l=o.scaleStartScale*a;if(l=Math.max(c,Math.min(u,l)),Math.abs(l-n.scale)>.001){const t=e.getBoundingClientRect(),a=t.left+t.width/2+o.scaleCenterX*n.scale+n.translateX,i=t.top+t.height/2+o.scaleCenterY*n.scale+n.translateY;n.scale=l,n.translateX=a-t.left-t.width/2-o.scaleCenterX*n.scale,n.translateY=i-t.top-t.height/2-o.scaleCenterY*n.scale,s()}}},p=t=>{if(0===t.touches.length){const t=n.isDragging&&!i.hasDragged;n.isDragging=!1,n.isZooming=!1,o.isScaling=!1,e.classList.remove("dragging","zooming"),t&&l&&l()}else 1===t.touches.length&&(n.isZooming=!1,o.isScaling=!1,e.classList.remove("zooming"))};return{handleTouchStart:d,handleModalTouchStart:m,handleTouchMove:v,handleTouchEnd:p,addTouchEventListeners:t=>{e.addEventListener("touchstart",d,{passive:!1}),t?(t.addEventListener("touchstart",m,{passive:!1}),t.addEventListener("touchmove",v,{passive:!1}),t.addEventListener("touchend",p,{passive:!0}),t.addEventListener("touchcancel",p,{passive:!0})):(e.addEventListener("touchmove",v,{passive:!1}),e.addEventListener("touchend",p,{passive:!0}),e.addEventListener("touchcancel",p,{passive:!0}))},removeTouchEventListeners:t=>{e.removeEventListener("touchstart",d),t?(t.removeEventListener("touchstart",m),t.removeEventListener("touchmove",v),t.removeEventListener("touchend",p),t.removeEventListener("touchcancel",p)):(e.removeEventListener("touchmove",v),e.removeEventListener("touchend",p),e.removeEventListener("touchcancel",p))}}}(e,n,i,a);return{state:n.state,handleWheelZoom:i.handleWheelZoom,initialize:()=>{e.style.willChange="transform",e.style.backfaceVisibility="hidden",e.style.perspective="1000px",o.addMouseEventListeners(),r.addTouchEventListeners(l)},cleanup:()=>{i.resetTransform(),o.removeMouseEventListeners(),r.removeTouchEventListeners(l)},resetTransform:i.resetTransform,updateTransform:i.updateTransform}}const ul=["src","alt","data-relative-src","data-original-src"],dl=Oa(i({__name:"ImageElement",props:{src:{},alt:{},width:{},height:{},isResizing:{type:Boolean},resizeWidth:{},resizeHeight:{},useThumbnail:{type:Boolean}},emits:["doubleClick","click","load","error"],setup(e,{expose:t}){const a=e,l=n(),i=o((()=>(e=>{if(!e)return"";if(!e.startsWith("http"))return e;try{return new URL(e).pathname}catch{return e}})(a.src))),r=o((()=>{return(e=i.value)?e.startsWith("http")?e:_a.getResourceURL(e):"";var e})),s=o((()=>({display:"block",maxWidth:"100%",margin:"0",padding:"0",verticalAlign:"baseline",transform:"translateY(0)",transition:"none",willChange:"transform",lineHeight:"normal",fontSize:"inherit",width:a.isResizing?a.resizeWidth:a.width||"",height:a.isResizing?a.resizeHeight:a.height||"",imageRendering:"auto",objectFit:"contain"})));return t({imageElement:l}),(e,t)=>(m(),f("img",{ref_key:"imageElement",ref:l,src:r.value,alt:e.alt||"",style:k(s.value),"data-relative-src":i.value,"data-original-src":i.value,contenteditable:"false",loading:"eager",class:"cursor-pointer",onDblclick:t[0]||(t[0]=t=>e.$emit("doubleClick",t)),onClick:t[1]||(t[1]=t=>e.$emit("click",t)),onLoad:t[2]||(t[2]=t=>e.$emit("load",t)),onError:t[3]||(t[3]=t=>e.$emit("error",t))},null,44,ul))}}),[["__scopeId","data-v-39865b3e"]]),ml=["data-handle-position","onMousedown","onTouchstart"],vl=["data-handle-position","onMousedown","onTouchstart"],pl=Oa(i({__name:"ImageResizeHandles",props:{isEditable:{type:Boolean},isSelected:{type:Boolean}},emits:["resizeStart","touchStart"],setup(e){const t=e,a=x(["top-left","top-right","bottom-left","bottom-right"]),l=x(["top","right","bottom","left"]),n=e=>({"top-left":"nw-resize","top-right":"ne-resize","bottom-left":"sw-resize","bottom-right":"se-resize",top:"n-resize",bottom:"s-resize",left:"w-resize",right:"e-resize"}[e]||"default"),i=e=>{const i={position:"absolute",background:"#2d8cf0",border:"1px solid white",borderRadius:"50%",zIndex:"100",display:t.isSelected&&t.isEditable?"block":"none",visibility:t.isSelected&&t.isEditable?"visible":"hidden",opacity:t.isSelected&&t.isEditable?"1":"0"};if(a.includes(e)){const t={width:"8px",height:"8px",cursor:n(e)},a={"top-left":{top:"-1px",left:"-1px"},"top-right":{top:"-1px",right:"-1px"},"bottom-left":{bottom:"-1px",left:"-1px"},"bottom-right":{bottom:"-1px",right:"-1px"}};return{...i,...t,...a[e]}}if(l.includes(e)){const t={width:"6px",height:"6px",cursor:n(e)},a={top:{top:"-1px",left:"50%",transform:"translateX(-50%)"},bottom:{bottom:"-1px",left:"50%",transform:"translateX(-50%)"},left:{left:"-1px",top:"50%",transform:"translateY(-50%)"},right:{right:"-1px",top:"50%",transform:"translateY(-50%)"}};return{...i,...t,...a[e]}}return i};return(e,t)=>e.isEditable&&e.isSelected?(m(),f(L,{key:0},[(m(!0),f(L,null,S(g(a),(t=>(m(),f("div",{key:t,class:y(["resize-handle",`handle-${t}`]),"data-handle-position":t,"data-center-handle":!1,style:k(i(t)),onMousedown:a=>e.$emit("resizeStart",a,t),onTouchstart:a=>e.$emit("touchStart",a,t)},null,46,ml)))),128)),(m(!0),f(L,null,S(g(l),(t=>(m(),f("div",{key:t,class:y(["resize-handle",`handle-${t}`]),"data-handle-position":t,"data-center-handle":!0,style:k(i(t)),onMousedown:a=>e.$emit("resizeStart",a,t),onTouchstart:a=>e.$emit("touchStart",a,t)},null,46,vl)))),128))],64)):C("",!0)}}),[["__scopeId","data-v-e8fc578d"]]);const gl={key:0,class:"resize-info"},hl=Oa(i({__name:"ImageNodeView",props:{node:{},updateAttributes:{type:Function},selected:{type:Boolean},editor:{},useThumbnail:{type:Boolean},decorations:{},deleteNode:{type:Function},view:{},getPos:{type:Function},innerDecorations:{},extension:{},HTMLAttributes:{}},setup(e,{expose:t}){const a=e,l=n(),i=n(),s=o((()=>a.editor.isEditable)),c=o((()=>a.selected)),u=T(!1),h=T(""),b=T(""),{handleResizeStart:x,handleTouchStart:L,updateResizeState:S}=function(e){const{isResizing:t,resizeWidth:a,resizeHeight:l,updateAttributes:i,node:o}=e,r=n(0),s=n(0),c=n(0),u=n(0),d=n(null),m=n(1),v=e=>{if(!t.value||!d.value)return;const a=e.clientX-r.value,l=e.clientY-s.value;g(a,l)},p=e=>{if(!t.value||!d.value)return;const a=e.touches[0];if(!a)return;const l=a.clientX-r.value,n=a.clientY-s.value;g(l,n)},g=(e,t)=>{if(!d.value)return;let n=c.value,i=u.value;switch(d.value){case"top-left":n=c.value-e,i=u.value-t;break;case"top-right":n=c.value+e,i=u.value-t;break;case"bottom-left":n=c.value-e,i=u.value+t;break;case"bottom-right":n=c.value+e,i=u.value+t;break;case"top":i=u.value-t,n=i*m.value;break;case"bottom":i=u.value+t,n=i*m.value;break;case"left":n=c.value-e,i=n/m.value;break;case"right":n=c.value+e,i=n/m.value}n=Math.max(50,n),i=Math.max(50,i),n=Math.min(800,n),i=Math.min(600,i),a.value=`${n}px`,l.value=`${i}px`},h=()=>{w()},f=()=>{w()},w=()=>{t.value&&(a.value&&l.value&&i({width:a.value,height:l.value}),t.value=!1,d.value=null,a.value="",l.value="",document.removeEventListener("mousemove",v),document.removeEventListener("mouseup",h),document.removeEventListener("touchmove",p),document.removeEventListener("touchend",f),document.body.style.overflow="")};return{handleResizeStart:(e,a)=>{e.preventDefault(),e.stopPropagation(),t.value=!0,d.value=a,r.value=e.clientX,s.value=e.clientY;const l=parseInt(o.attrs.width)||200,n=parseInt(o.attrs.height)||150;c.value=l,u.value=n,m.value=l/n,document.addEventListener("mousemove",v),document.addEventListener("mouseup",h),document.body.style.overflow="hidden"},handleTouchStart:(e,a)=>{e.preventDefault(),e.stopPropagation();const l=e.touches[0];if(!l)return;t.value=!0,d.value=a,r.value=l.clientX,s.value=l.clientY;const n=parseInt(o.attrs.width)||200,i=parseInt(o.attrs.height)||150;c.value=n,u.value=i,m.value=n/i,document.addEventListener("touchmove",p),document.addEventListener("touchend",f),document.body.style.overflow="hidden"},updateResizeState:(e,n,i)=>{a.value=e,l.value=n,t.value=i}}}({isResizing:u,resizeWidth:h,resizeHeight:b,updateAttributes:a.updateAttributes,node:a.node}),R=()=>{var e;const t=null==(e=i.value)?void 0:e.imageElement;t&&a.node.attrs.src&&function(e,t){const a=document.createElement("div");a.classList.add("modal-overlay");const l=document.createElement("img");l.alt="图片预览",a.appendChild(l),document.body.appendChild(a),a.classList.add("modal-overlay-active");const n="true"===e.dataset.isOriginal,i=e.dataset.thumbnailSrc||t,o=t.startsWith("http://")||t.startsWith("https://");if(o||!i.includes(Ia)||n)l.src=e.src,l.style.opacity="1";else{l.src=e.src,l.style.opacity="0.5";const n=document.createElement("div");n.classList.add("loading-spinner"),a.appendChild(n);const i=new Image,o=Date.now(),r=500;i.onload=()=>{const e=Date.now()-o,t=()=>{n.style.display="none",l.src=i.src,l.style.opacity="1",l.dataset.originalFullUrl=i.src};e<r?setTimeout(t,r-e):t()},i.src=_a.getResourceURL(t.replace(Ia,""))}const r=()=>{a.classList.remove("modal-overlay-active"),a.addEventListener("transitionend",(()=>{if(!a.classList.contains("modal-overlay-active")){if(!o&&l.dataset.originalFullUrl&&i.includes(Ia)&&!n){e.src=l.dataset.originalFullUrl,e.dataset.isOriginal="true";const a=t.replace(Ia,"");e.dataset.originalSrc=a}document.body.removeChild(a),c(),s.cleanup()}}),{once:!0})},s=cl(l,{minScale:.5,maxScale:3},r,a);s.initialize();const c=il(a,r,s.handleWheelZoom)}(t,a.node.attrs.src)},A=()=>{s.value&&R()},E=()=>{s.value||R()},_=()=>{},I=e=>{},z=o((()=>({display:"inline-block",position:"relative",margin:"0",padding:"4px",verticalAlign:"baseline",lineHeight:"1",transform:"translateY(0)",transition:"none",zIndex:"1",willChange:"transform",boxSizing:"border-box",width:"fit-content",maxWidth:"100%",border:"0",userSelect:"none",pointerEvents:"auto"}))),P=e=>{if(e.stopPropagation(),!s.value)return;const{view:t}=a.editor;if(t){const e=t.posAtDOM(l.value,0);null!==e&&t.dispatch(t.state.tr.setSelection(Ft.near(t.state.doc.resolve(e))))}};return r((()=>{})),M((()=>{})),t({updateNode:e=>{var t,l;if(e.attrs.src,a.node.attrs.src,e.attrs.width!==a.node.attrs.width){const a=null==(t=i.value)?void 0:t.imageElement;a&&(a.style.width=e.attrs.width||"")}if(e.attrs.height!==a.node.attrs.height){const t=null==(l=i.value)?void 0:l.imageElement;t&&(t.style.height=e.attrs.height||"")}},updateResizeState:(e,t,a)=>{S(e,t,a)}}),(e,t)=>(m(),d(g(Ge),{ref_key:"imageWrapper",ref:l,class:y(["image-wrapper",{"readonly-image":!s.value,"ProseMirror-selectednode":c.value&&s.value,resizing:u.value}]),style:k(z.value),onClick:P},{default:v((()=>[p(dl,{ref_key:"imageElementRef",ref:i,src:e.node.attrs.src,alt:e.node.attrs.alt,width:e.node.attrs.width,height:e.node.attrs.height,"is-resizing":u.value,"resize-width":h.value,"resize-height":b.value,"use-thumbnail":e.useThumbnail,onDoubleClick:A,onClick:E,onLoad:_,onError:I},null,8,["src","alt","width","height","is-resizing","resize-width","resize-height","use-thumbnail"]),p(pl,{"is-editable":s.value,"is-selected":c.value,onResizeStart:g(x),onTouchStart:g(L)},null,8,["is-editable","is-selected","onResizeStart","onTouchStart"]),u.value&&h.value&&b.value?(m(),f("div",gl,w(Math.round(parseFloat(h.value)))+" × "+w(Math.round(parseFloat(b.value))),1)):C("",!0)])),_:1},8,["class","style"]))}}),[["__scopeId","data-v-2e1577b4"]]),fl=(e=!1)=>lt.extend({draggable:!0,inline:!0,group:"inline",atom:!0,addOptions(){var t;return{...null==(t=this.parent)?void 0:t.call(this),transformSrc:e=>Pa(e),getFullUrl:t=>Ba(t,e)}},addAttributes(){var e;return{...null==(e=this.parent)?void 0:e.call(this),width:{default:null,renderHTML:e=>e.width?{width:e.width}:{}},height:{default:null,renderHTML:e=>e.height?{height:e.height}:{}},src:{default:null,parseHTML:e=>{const t=e.getAttribute("src")||"";return Pa(t)},renderHTML:e=>({src:e.src})}}},addNodeView:()=>Qe(hl),addStorage:()=>({transformSrc:Pa,getFullUrl:t=>Ba(t,e)})}).configure({allowBase64:!1}),wl=fl(!0);const yl=()=>{const e=n(),t=(e,t,a,l)=>{ta.debug("files: ",e),e.length&&e.forEach((e=>{e.type.startsWith("image/")&&$a(e,t,a,l).then((e=>{ta.debug("Image uploaded, relative path:",e);const{handleImageUploadSuccess:a}=function(){const e=()=>{document.querySelectorAll(".image-wrapper.ProseMirror-selectednode").forEach((e=>{e.classList.remove("ProseMirror-selectednode")}))},t=e=>!!(e&&e.view&&e.view.dom)||(ta.error("Editor view dom not found"),!1),a=(e,t)=>{var a;const l=e.view.dom.querySelectorAll(".image-wrapper img");ta.debug("Found image elements:",l.length);let n=null;const i=null==(a=e.storage.image)?void 0:a.transformSrc;return Array.from(l).filter((e=>e instanceof HTMLImageElement)).forEach((e=>{const a=e.dataset.relativeSrc,l=e.getAttribute("src");ta.debug("Checking img:",{dataRelativeSrc:a,src:l,lookingFor:t});let o=l||"";if(i&&l)o=i(l);else if(l&&l.startsWith("http"))try{o=new URL(l).pathname}catch{ta.warn("Failed to parse URL:",l)}(a===t||o===t||t.endsWith(o))&&(ta.debug("Found matching image element"),n=e.closest(".image-wrapper"))})),n},l=e=>{var t,a;ta.debug("Found target wrapper, adding visual feedback"),null==(t=e.classList)||t.add("ProseMirror-selectednode"),null==(a=e.scrollIntoView)||a.call(e,{behavior:"smooth",block:"center"})},n=e=>{try{const t=document.createElement("div");t.className="upload-success-effect",t.style.cssText="\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        border-radius: 0.25rem;\n        background-color: rgba(90, 214, 150, 0.2);\n        z-index: 5;\n        pointer-events: none;\n        opacity: 0;\n      ",e.appendChild(t),t.animate([{opacity:.7},{opacity:0}],{duration:800,easing:"ease-out"}).onfinish=()=>{t.remove()},e.animate([{outline:"2px solid #5ad696",outlineOffset:"3px"},{outline:"2px solid #2d8cf0",outlineOffset:"2px"}],{duration:800,easing:"ease-out"})}catch(t){ta.error("Error adding upload success animation:",t)}},i=e=>{var t,a;ta.debug("No matching image found, selecting last wrapper");const l=e.view.dom.querySelectorAll(".image-wrapper");if(l.length>0){const e=l[l.length-1];null==(t=e.classList)||t.add("ProseMirror-selectednode"),null==(a=e.scrollIntoView)||a.call(e,{behavior:"smooth",block:"center"})}};return{handleImageUploadSuccess:(o,r)=>{setTimeout((()=>{try{if(e(),!t(o))return;const s=a(o,r);s?(l(s),n(s)):i(o)}catch(s){ta.error("Error selecting image after upload:",s)}}),300)},clearSelectedImages:e,validateEditor:t,findTargetImageWrapper:a,selectAndScrollToImage:l,addUploadSuccessAnimation:n,selectLastImage:i}}();a(t,e)}))}))},{imageHandleCallback:a}=function(){const e=e=>e.filter((e=>e.type.startsWith("image/"))),t=e=>!(!e||!e.src),a=(e,t,a,l)=>{if(ta.debug("Editor is already handling one image, skipping duplicate processing"),e.length>1){ta.debug("Processing additional images:",e.length-1);for(let n=1;n<e.length;n++)$a(e[n],t,a,l)}},l=(e,t,a,l)=>{ta.debug("Processing all images:",e.length),e.forEach((e=>{$a(e,t,a,l)}))};return{imageHandleCallback:(n,i,o,r,s)=>{if(ta.debug("imageHandleCallback called with:",{filesCount:i.length,hasAttrs:!!o}),!i.length)return;const c=e(i);0!==c.length?t(o)?a(c,n,r,s):l(c,n,r,s):ta.debug("No image files found in event")},filterImageFiles:e,shouldSkipFirstImage:t,processAdditionalImages:a,processAllImages:l}}();return{imageInputRef:e,handleImageChange:async(e,a,l,n)=>{const i=e.target;if(i.files&&i.files.length>0){const e=Array.from(i.files);t(e,a,l,n)}},handleImageChangeCallback:t,imageHandleCallback:a}},bl={URL:"/core/users",online:async()=>(await La(bl.URL+"/online")).data,info:async()=>(await La(bl.URL+"/me")).data,changeAvatar:async e=>{const t=new FormData;t.append("file",e);return(await Ra(bl.URL+"/me/avatar",t,{headers:{"Content-Type":"multipart/form-data"}})).data},updateNotificationReceiveType:async e=>(await Ma(bl.URL+"/me/notification-settings",{type:e})).data,searchUser:async e=>(await La(bl.URL,{username:e})).data};class kl{constructor(e){t(this,"editor"),t(this,"options"),t(this,"element"),t(this,"index"),t(this,"nodes"),t(this,"items"),t(this,"command"),this.editor=e.editor,this.options=e}static create(e){return()=>new kl(e)}onStart(e){this.index=0,this.nodes=[],this.items=[],this.command=e.command,this.element=document.createElement("div"),this.element.classList.add("dropdown-menu");for(const t of this.options.classes??[])this.element.classList.add(t);for(const[t,a]of Object.entries(this.options.attributes??{}))this.element.setAttribute(t,a);document.body.appendChild(this.element),this.onUpdate(e)}onUpdate(e){this.element&&void 0!==this.index&&this.nodes&&this.items&&(this.items=e.items,this.command=e.command,this.render(),e.clientRect&&this.updatePosition(e.clientRect))}onKeyDown(e){if(!this.element||void 0===this.index||!this.nodes||!this.items)return!1;if("Escape"===e.event.key)return this.hide(),!0;if("Enter"===e.event.key){const e=this.items[this.index];return e&&this.command&&this.command({id:e.id,label:e.username,avatar:e.avatar}),!0}return"ArrowUp"===e.event.key?(this.selectItem(this.index-1<0?this.items.length-1:this.index-1,!0),!0):"ArrowDown"===e.event.key&&(this.selectItem(this.index+1>=this.items.length?0:this.index+1,!0),!0)}onExit(){this.hide(),this.cleanup()}selectItem(e,t){if(this.element&&void 0!==this.index&&this.nodes&&this.items){this.index=Math.max(0,Math.min(e,this.items.length-1));for(let e=0;e<this.nodes.length;e++)e===this.index?this.nodes[e].classList.add("is-selected"):this.nodes[e].classList.remove("is-selected");t&&this.nodes[this.index]&&jt(this.nodes[this.index],{behavior:"smooth",block:"nearest",inline:"nearest",boundary:e=>e!==this.element})}}render(){var e;if(this.element&&void 0!==this.index&&this.items){for(;this.element.firstChild;)this.element.removeChild(this.element.firstChild);if(this.index=Math.max(0,Math.min(this.index,Math.max(0,this.items.length-1))),this.items.length){this.nodes=[];for(let e=0;e<this.items.length;e++){const t=this.items[e],a=document.createElement("button");if(a.setAttribute("type","button"),t.avatar){const e=document.createElement("img");e.classList.add("dropdown-avatar"),e.src=_a.getResourceURL(t.avatar),e.alt=t.username,e.loading="lazy",a.appendChild(e)}const l=document.createElement("span");l.textContent=t.username,a.appendChild(l),e===this.index&&a.classList.add("is-selected"),a.addEventListener("click",(()=>{this.command&&this.command({id:t.id,label:t.username,avatar:t.avatar})})),this.nodes.push(a)}for(const e of this.nodes)this.element.appendChild(e);this.show()}else{const t=document.createElement("div");t.classList.add("item"),t.textContent=(null==(e=this.options.dictionary)?void 0:e.empty)??"...",this.element.appendChild(t),this.show()}}}updatePosition(e){if(!this.element)return;const t=e();if(!t)return;const a=window.innerHeight,l=window.innerWidth;this.element.style.position="fixed",this.element.style.zIndex="10000",this.element.style.left=`${t.left}px`,this.element.style.top=`${t.bottom+8}px`,this.element.style.visibility="hidden",this.element.style.display="block";const n=this.element.getBoundingClientRect(),i=n.height,o=n.width;this.element.style.visibility="visible";let r=t.left,s=t.bottom+8,c=!1;const u=a-t.bottom-8,d=t.top-8;u<i&&d>i?(s=t.top-i-8,c=!0):u<i&&d<i&&(d>u&&(s=t.top-i-8,c=!0),(c&&d<i||!c&&u<i)&&(this.element.style.maxHeight=Math.max(d,u)-16+"px",this.element.style.overflowY="auto")),r+o>l&&(r=Math.max(8,l-o-8)),r<8&&(r=8),this.element.style.left=`${r}px`,this.element.style.top=`${s}px`,this.element.classList.toggle("dropdown-menu-above",c),this.element.classList.toggle("dropdown-menu-below",!c)}show(){this.element&&(this.element.style.display="block")}hide(){this.element&&(this.element.style.display="none")}cleanup(){this.element&&this.element.parentNode&&this.element.parentNode.removeChild(this.element),this.element=void 0,this.nodes=void 0,this.items=void 0,this.command=void 0}}const xl=nt.extend({name:"mention",addAttributes(){var e;return{...null==(e=this.parent)?void 0:e.call(this),avatar:{default:null,parseHTML:e=>e.getAttribute("data-avatar"),renderHTML:e=>e.avatar?{"data-avatar":e.avatar}:{}}}},renderHTML({node:e,HTMLAttributes:t}){const a=e.attrs,l=[["span",{class:"mention-name"},`@${a.label}`]];if(a.avatar){const e=_a.getResourceURL(a.avatar);l.push(["img",{class:"mention-avatar",src:e,alt:a.label,loading:"lazy"},""])}return["span",{...t,"data-type":"mention","data-id":a.id,"data-label":a.label,"data-avatar":a.avatar||"",class:"mention",contenteditable:"false"},...l]}}).configure({suggestion:{items:async({query:e})=>{if(!e)return[];return(await bl.searchUser(e)).data||[]},render:()=>{let e;return{onStart:t=>{e=kl.create({editor:t.editor,dictionary:{empty:"..."}})(),e.onStart(t)},onUpdate:t=>{null==e||e.onUpdate(t)},onKeyDown:t=>(null==e?void 0:e.onKeyDown(t))??!1,onExit:()=>{null==e||e.onExit()}}}}});class Cl{constructor(e){t(this,"editor"),t(this,"options"),t(this,"element"),t(this,"index"),t(this,"nodes"),t(this,"items"),this.editor=e.editor,this.options=e}static create(e){return()=>new Cl(e)}onStart(e){this.index=0,this.nodes=[],this.items=[],this.element=document.createElement("div"),this.element.classList.add("slash-menu");for(const t of this.options.classes??[])this.element.classList.add(t);for(const[t,a]of Object.entries(this.options.attributes??{}))this.element.setAttribute(t,a);document.body.appendChild(this.element),this.onUpdate(e)}onUpdate(e){this.element&&void 0!==this.index&&this.nodes&&this.items&&(this.items=e.items,this.render(),e.clientRect&&this.updatePosition(e.clientRect))}onKeyDown(e){if(!this.element||void 0===this.index||!this.nodes||!this.items)return!1;if("Escape"===e.event.key)return this.hide(),!0;if("Enter"===e.event.key){const e=this.items[this.index];return e&&"string"!=typeof e&&e.action&&e.action(this.editor),!0}if("ArrowUp"===e.event.key){const e=this.index-1,t=this.items[e]&&"string"==typeof this.items[e]?e-1:e;return this.selectItem(t<0?this.items.length-1:t,!0),!0}if("ArrowDown"===e.event.key){const e=this.index+1,t=this.items[e]&&"string"==typeof this.items[e]?e+1:e;return this.selectItem(t>=this.items.length?0:t,!0),!0}return!1}onExit(){this.hide(),this.cleanup()}selectItem(e,t){if(this.element&&void 0!==this.index&&this.nodes&&this.items){this.index=Math.max(0,Math.min(e,this.items.length-1));for(let e=0;e<this.nodes.length;e++)e===this.index?this.nodes[e].setAttribute("data-active","true"):this.nodes[e].removeAttribute("data-active");t&&this.nodes[this.index]&&jt(this.nodes[this.index],{block:"center",scrollMode:"if-needed",boundary:e=>e!==this.element})}}render(){var e;if(this.element&&void 0!==this.index&&this.items){for(;this.element.firstChild;)this.element.removeChild(this.element.firstChild);if(this.index=Math.max(0,Math.min(this.index,Math.max(0,this.items.length-1))),this.items.length){this.nodes=[];for(let e=0;e<this.items.length;e++){const t=this.items[e];if("|"===t){const e=document.createElement("div");e.classList.add("slash-menu-divider"),this.nodes.push(e)}else{const a=document.createElement("button");if(a.classList.add("slash-menu-button"),a.setAttribute("type","button"),t.icon){const e=document.createElement("div");e.classList.add("slash-menu-button-icon"),e.innerHTML=t.icon,a.appendChild(e)}const l=document.createElement("div");if(l.classList.add("slash-menu-button-name"),l.textContent=t.name,a.appendChild(l),t.shortcut){const e=document.createElement("div");e.classList.add("slash-menu-button-shortcut"),e.textContent=t.shortcut.replace(/mod/i,navigator.userAgent.includes("Mac")?"⌘":"Ctrl").replace(/ctrl|control/i,"Ctrl").replace(/cmd|command/i,"⌘").replace(/shift/i,"Shift").replace(/alt|option/i,"Alt"),a.appendChild(e)}e===this.index&&a.setAttribute("data-active","true"),a.addEventListener("click",(()=>{t.action&&t.action(this.editor)})),a.addEventListener("mouseover",(()=>{this.index!==e&&this.selectItem(e)})),this.nodes.push(a)}}this.element.append(...this.nodes),this.nodes[this.index]&&jt(this.nodes[this.index],{block:"center",scrollMode:"if-needed",boundary:e=>e!==this.element})}else{const t=document.createElement("div");t.classList.add("slash-menu-empty"),t.textContent=(null==(e=this.options.dictionary)?void 0:e.empty)||"未找到结果",this.element.appendChild(t)}this.show()}}updatePosition(e){if(!this.element)return;const t=e();if(!t)return;const a=window.innerHeight,l=window.innerWidth;this.element.style.position="fixed",this.element.style.zIndex="10000",this.element.style.left=`${t.left}px`,this.element.style.top=`${t.bottom+8}px`,this.element.style.visibility="hidden",this.element.style.display="block";const n=this.element.getBoundingClientRect(),i=n.height,o=n.width;this.element.style.visibility="visible";let r=t.left,s=t.bottom+8,c=!1;const u=a-t.bottom-8,d=t.top-8;u<i&&d>i?(s=t.top-i-8,c=!0):u<i&&d<i&&(d>u&&(s=t.top-i-8,c=!0),(c&&d<i||!c&&u<i)&&(this.element.style.maxHeight=Math.max(d,u)-16+"px",this.element.style.overflowY="auto")),r+o>l&&(r=Math.max(8,l-o-8)),r<8&&(r=8),this.element.style.left=`${r}px`,this.element.style.top=`${s}px`,this.element.classList.toggle("slash-menu-above",c),this.element.classList.toggle("slash-menu-below",!c)}show(){this.element&&(this.element.style.display="block")}hide(){this.element&&(this.element.style.display="none")}cleanup(){this.element&&(this.element.remove(),this.element=void 0),this.index=void 0,this.items=void 0,this.nodes=void 0}}const Ll=at.create({name:"slashMenu",addOptions:()=>({items:[],dictionary:{lineEmpty:"",lineSlash:" ...",queryEmpty:"..."},imageUploadTrigger:void 0,modalTrigger:void 0}),addProseMirrorPlugins(){return[it({editor:this.editor,pluginKey:new Ht(`${this.name}-suggestion`),char:"/",allow:({editor:e,state:t,range:a})=>{const{$from:l}=t.selection,n=l.parent;return"codeBlock"!==n.type.name&&"paragraph"===n.type.name},items:({query:e})=>{const t=[];for(const l of this.options.items)if("|"!==l){if(""!==e){const t=e.toLowerCase();if(!l.name.toLowerCase().includes(t)&&!l.keywords.toLowerCase().includes(t))continue}t.push({...l,action:t=>{const{state:a,dispatch:n}=t.view,i=a.selection.$from,o=i.pos-(e.length+1);n(a.tr.deleteRange(o,i.pos)),"image"===l.id&&this.options.imageUploadTrigger?this.options.imageUploadTrigger():l.action(t),t.view.focus()}})}else t.push(l);const a=[];for(let l=0;l<t.length;l++){const e=t[l];if("|"===e){if(0===l||l===t.length-1)continue;if("|"===t[l+1])continue;if(0===a.length)continue;if("|"===a[a.length-1])continue}a.push(e)}return a},render:Cl.create({editor:this.editor,dictionary:{empty:this.options.dictionary.queryEmpty}})}),new Ot({key:new Ht(`${this.name}-placeholder`),props:{decorations:e=>{const{$from:t}=e.selection,a=t.parent;if("paragraph"!==a.type.name)return null;const l=[],n=0===a.content.size,i="/"===a.textContent;return 1===t.depth?(n&&l.push(Nt.node(t.start()-1,t.end()+1,{class:"slash-menu-placeholder","data-placeholder":this.options.dictionary.lineEmpty})),i&&l.push(Nt.node(t.start()-1,t.end()+1,{class:"slash-menu-placeholder","data-placeholder":` ${this.options.dictionary.lineSlash}`})),qt.create(e.doc,l)):null}}})]}}),Sl={TextHeader120Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M16.573 3.823a.75.75 0 0 0-1.058.53c-.255 1.138-1.308 2.608-2.681 3.523a.75.75 0 1 0 .832 1.248A8.769 8.769 0 0 0 15.5 7.47V15.5a.75.75 0 0 0 1.5 0V4.516a.75.75 0 0 0-.427-.693zM3.5 4.5a.75.75 0 1 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 1 0-1.5 0V9h-5V4.5z" fill="currentColor"></path></g></svg>',TextHeader220Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M3.5 4.5a.75.75 0 0 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 0 0-1.5 0V9h-5V4.5zm11.25.75c-1.292 0-2.25 1.124-2.25 2.25a.75.75 0 0 1-1.5 0c0-1.874 1.551-3.75 3.75-3.75c1.403 0 2.475.793 2.973 1.915c.49 1.106.41 2.488-.33 3.72c-.385.643-.958 1.16-1.527 1.607c-.265.209-.545.414-.816.613l-.067.049c-.295.217-.582.43-.858.65c-.892.715-1.569 1.449-1.794 2.446h4.919a.75.75 0 0 1 0 1.5H11.5a.75.75 0 0 1-.75-.75c0-2.099 1.226-3.396 2.437-4.366c.303-.243.614-.473.909-.69l.062-.045c.276-.202.535-.393.78-.586c.534-.42.929-.799 1.169-1.199c.51-.85.52-1.718.244-2.341c-.27-.608-.822-1.023-1.601-1.023z" fill="currentColor"></path></g></svg>',TextHeader320Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M3.5 4.5a.75.75 0 0 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 0 0-1.5 0V9h-5V4.5zm8.97 1.958c.086-.295.216-.573.467-.784c.245-.206.693-.424 1.563-.424c.777 0 1.257.3 1.555.648c.32.374.445.825.445 1.102c0 .356-.091.92-.448 1.38c-.327.423-.965.87-2.302.87a.75.75 0 0 0 0 1.5c.446 0 1.198.11 1.81.42c.59.298.94.711.94 1.33c0 .84-.258 1.385-.593 1.72c-.338.338-.824.53-1.407.53c-.68 0-1.152-.116-1.458-.3c-.275-.164-.47-.414-.557-.847a.75.75 0 1 0-1.47.294c.163.817.593 1.442 1.255 1.84c.632.379 1.41.513 2.23.513c.917 0 1.806-.308 2.468-.97c.665-.665 1.032-1.62 1.032-2.78c0-1.234-.695-2.034-1.481-2.512c.283-.201.522-.434.72-.689C17.868 8.485 18 7.551 18 7c0-.63-.25-1.428-.805-2.077c-.577-.675-1.472-1.173-2.695-1.173c-1.13 0-1.95.29-2.528.776c-.571.48-.816 1.078-.942 1.516a.75.75 0 0 0 1.44.416z" fill="currentColor"></path></g></svg>',TextBold20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M5 4.5A1.5 1.5 0 0 1 6.5 3h3.88c2.364 0 4.12 1.934 4.12 4.12c0 .819-.247 1.606-.68 2.269c.842.749 1.427 1.849 1.427 3.241c0 2.775-2.318 4.37-4.367 4.37H6.5A1.5 1.5 0 0 1 5 15.5v-11zM8 6v2.25h2.38c.625 0 1.12-.516 1.12-1.13A1.12 1.12 0 0 0 10.38 6H8zm0 5.25V14h2.88c.691 0 1.367-.537 1.367-1.37c0-.84-.684-1.38-1.367-1.38H8z" fill="currentColor"></path></g></svg>',TextItalic20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M8 3.25a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-3.235L8.592 15.5h2.658a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3.235L11.408 4H8.75A.75.75 0 0 1 8 3.25z" fill="currentColor"></path></g></svg>',TextUnderline24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M6 4.5a1 1 0 0 1 2 0v6.001c-.003 3.463 1.32 4.999 4.247 4.999c2.928 0 4.253-1.537 4.253-5v-6a1 1 0 1 1 2 0v6c0 4.54-2.18 7-6.253 7S5.996 15.039 6 10.5v-6zM7 21a1 1 0 1 1 0-2h10.5a1 1 0 1 1 0 2H7z" fill="currentColor"></path></g></svg>',TextStrikethrough20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M6.252 3.702A6.56 6.56 0 0 1 10 2.5c2.783 0 4.489 1.485 5.1 2.3a.75.75 0 0 1-1.2.9C13.511 5.182 12.217 4 10 4a5.06 5.06 0 0 0-2.877.923C6.331 5.489 6 6.105 6 6.5c0 .78.376 1.285 1.11 1.71c.18.105.377.2.586.29H5.162c-.408-.523-.662-1.178-.662-2c0-1.105.794-2.114 1.752-2.798zM16.5 10a.75.75 0 0 1 0 1.5h-1.662c.408.523.662 1.178.662 2c0 1.358-.874 2.376-1.912 3.014c-1.042.641-2.367.986-3.588.986c-1.142 0-2.133-.129-2.992-.498c-.877-.378-1.563-.982-2.132-1.836a.75.75 0 1 1 1.248-.832c.43.646.901 1.042 1.477 1.29c.594.255 1.354.376 2.4.376c.966 0 2.015-.28 2.801-.764C13.593 14.75 14 14.141 14 13.5c0-.78-.376-1.285-1.11-1.71c-.18-.105-.377-.2-.586-.29H3.5a.75.75 0 0 1 0-1.5h13z" fill="currentColor"></path></g></svg>',Code20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M12.937 4.052a.75.75 0 0 0-1.373-.604l-5.5 12.5a.75.75 0 1 0 1.372.604l5.5-12.5zm1.356 9.793a.75.75 0 0 1-.137-1.052L16.303 10l-2.148-2.793a.75.75 0 0 1 1.188-.914l2.5 3.25a.75.75 0 0 1 0 .915l-2.5 3.25a.75.75 0 0 1-1.051.137zm-8.586-7.69a.75.75 0 0 1 .137 1.053L3.696 10l2.148 2.793a.75.75 0 1 1-1.188.915l-2.5-3.25a.75.75 0 0 1 0-.915l2.5-3.25a.75.75 0 0 1 1.051-.137z" fill="currentColor"></path></g></svg>',Image28Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 28 28"><g fill="none"><path d="M21.75 3A3.25 3.25 0 0 1 25 6.25v15.5A3.25 3.25 0 0 1 21.75 25H6.25A3.25 3.25 0 0 1 3 21.75V6.25A3.25 3.25 0 0 1 6.25 3h15.5zm.583 20.4l-7.807-7.68a.75.75 0 0 0-.968-.07l-.084.07l-7.808 7.68c.183.065.38.1.584.1h15.5c.204 0 .4-.035.583-.1l-7.807-7.68l7.807 7.68zM21.75 4.5H6.25A1.75 1.75 0 0 0 4.5 6.25v15.5c0 .208.036.408.103.593l7.82-7.692a2.25 2.25 0 0 1 3.026-.117l.129.117l7.82 7.692c.066-.185.102-.385.102-.593V6.25a1.75 1.75 0 0 0-1.75-1.75zm-3.25 3a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm0 1.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2z" fill="currentColor"></path></g></svg>',ArrowUndo16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M3 2.75a.75.75 0 0 1 1.5 0v3.095l2.673-2.673a4 4 0 0 1 5.657 5.656l-4.95 4.95a.75.75 0 1 1-1.06-1.06l4.95-4.95a2.5 2.5 0 0 0-3.536-3.536L5.966 6.5H8.25a.75.75 0 0 1 0 1.5h-4.4A.85.85 0 0 1 3 7.15v-4.4z" fill="currentColor"></path></g></svg>',ArrowRedo16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M13.002 2.75a.75.75 0 0 0-1.5 0v3.095L8.828 3.172a4 4 0 0 0-5.656 5.656l4.95 4.95a.75.75 0 1 0 1.06-1.06l-4.95-4.95a2.5 2.5 0 0 1 3.536-3.536L10.036 6.5H7.75a.75.75 0 0 0 0 1.5h4.4c.47 0 .85-.38.85-.85v-4.4z" fill="currentColor"></path></g></svg>',LineHorizontal120Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M2 9.75A.75.75 0 0 1 2.75 9h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 9.75z" fill="currentColor"></path></g></svg>',VideoClip24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6.25A3.25 3.25 0 0 1 5.25 3h13.5A3.25 3.25 0 0 1 22 6.25v11.5A3.25 3.25 0 0 1 18.75 21H5.25A3.25 3.25 0 0 1 2 17.75V6.25zm7.5 3.134v5.231c0 .57.61.932 1.11.658l4.786-2.616a.75.75 0 0 0 0-1.316L10.61 8.726a.75.75 0 0 0-1.11.658z" fill="currentColor"></path></g></svg>',FullScreenMaximize16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M4 3.5a.5.5 0 0 0-.5.5v1.614a.75.75 0 0 1-1.5 0V4a2 2 0 0 1 2-2h1.614a.75.75 0 0 1 0 1.5H4zm5.636-.75a.75.75 0 0 1 .75-.75H12a2 2 0 0 1 2 2v1.614a.75.75 0 0 1-1.5 0V4a.5.5 0 0 0-.5-.5h-1.614a.75.75 0 0 1-.75-.75zM2.75 9.636a.75.75 0 0 1 .75.75V12a.5.5 0 0 0 .5.5h1.614a.75.75 0 0 1 0 1.5H4a2 2 0 0 1-2-2v-1.614a.75.75 0 0 1 .75-.75zm10.5 0a.75.75 0 0 1 .75.75V12a2 2 0 0 1-2 2h-1.614a.75.75 0 1 1 0-1.5H12a.5.5 0 0 0 .5-.5v-1.614a.75.75 0 0 1 .75-.75z" fill="currentColor"></path></g></svg>',ResizeSmall20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zm3 3A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zm6-3A1.5 1.5 0 0 1 16 5.5v1a.5.5 0 0 0 1 0v-1A2.5 2.5 0 0 0 14.5 3h-1a.5.5 0 0 0 0 1h1zm0 12a1.5 1.5 0 0 0 1.5-1.5v-1a.5.5 0 0 1 1 0v1a2.5 2.5 0 0 1-2.5 2.5h-1a.5.5 0 0 1 0-1h1zm-9 0A1.5 1.5 0 0 1 4 14.5v-1.25a.5.5 0 0 0-1 0v1.25A2.5 2.5 0 0 0 5.5 17h1.25a.5.5 0 0 0 0-1H5.5z" fill="currentColor"></path></g></svg>',TextBulletListLtr16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M2.25 5a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zm0 4.25a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zm1.25 3a1.25 1.25 0 1 1-2.5 0a1.25 1.25 0 0 1 2.5 0zM5.75 3a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5zM5 8a.75.75 0 0 1 .75-.75h8.5a.75.75 0 0 1 0 1.5h-8.5A.75.75 0 0 1 5 8zm.75 3.5a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5z" fill="currentColor"></path></g></svg>',TextNumberListLtr16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M3.684 1.01c.193.045.33.21.33.402v3.294a.42.42 0 0 1-.428.412a.42.42 0 0 1-.428-.412V2.58a3.11 3.11 0 0 1-.664.435a.436.436 0 0 1-.574-.184a.405.405 0 0 1 .192-.552c.353-.17.629-.432.82-.661a2.884 2.884 0 0 0 .27-.388a.44.44 0 0 1 .482-.22zm-1.53 6.046a.401.401 0 0 1 0-.582l.002-.001V6.47l.004-.002l.008-.008a1.12 1.12 0 0 1 .103-.084a2.2 2.2 0 0 1 1.313-.435h.007c.32.004.668.084.947.283c.295.21.485.536.485.951c0 .452-.207.767-.488.992c-.214.173-.49.303-.714.409c-.036.016-.07.033-.103.049c-.267.128-.468.24-.61.39a.763.763 0 0 0-.147.22h1.635a.42.42 0 0 1 .427.411a.42.42 0 0 1-.428.412H2.457a.42.42 0 0 1-.428-.412c0-.51.17-.893.446-1.184c.259-.275.592-.445.86-.574c.043-.02.085-.04.124-.06c.231-.11.4-.19.529-.293c.12-.097.18-.193.18-.36c0-.148-.057-.23-.14-.289a.816.816 0 0 0-.448-.122a1.32 1.32 0 0 0-.818.289l-.005.005a.44.44 0 0 1-.602-.003zm.94 5.885a.42.42 0 0 1 .427-.412c.294 0 .456-.08.537-.15a.303.303 0 0 0 .11-.246c-.006-.16-.158-.427-.647-.427c-.352 0-.535.084-.618.137a.349.349 0 0 0-.076.062l-.003.004a.435.435 0 0 0 .01-.018v.001l-.002.002l-.002.004l-.003.006l-.005.008l.002-.003a.436.436 0 0 1-.563.165a.405.405 0 0 1-.191-.552v-.002l.002-.003l.003-.006l.008-.013a.71.71 0 0 1 .087-.12c.058-.067.142-.146.259-.22c.238-.153.59-.276 1.092-.276c.88 0 1.477.556 1.502 1.22c.012.303-.1.606-.339.84c.238.232.351.535.34.838c-.026.664-.622 1.22-1.503 1.22c-.502 0-.854-.122-1.092-.275a1.19 1.19 0 0 1-.326-.308a.71.71 0 0 1-.02-.033l-.008-.013l-.003-.005l-.001-.003v-.001l-.001-.001a.405.405 0 0 1 .19-.553a.436.436 0 0 1 .564.165l.003.004c.01.01.033.035.076.063c.083.053.266.137.618.137c.489 0 .641-.268.648-.428a.303.303 0 0 0-.11-.245c-.082-.072-.244-.151-.538-.151a.42.42 0 0 1-.427-.412zM7.75 3a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zm0 4a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zm0 4a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5z" fill="currentColor"></path></g></svg>',TaskListLtr24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M6.707 3.293a1 1 0 0 0-1.414 0L4 4.586l-.293-.293a1 1 0 0 0-1.414 1.414l1 1a1 1 0 0 0 1.414 0l2-2a1 1 0 0 0 0-1.414zm14.296 13.7H10L9.883 17A1 1 0 0 0 10 18.993h11.003l.117-.006a1 1 0 0 0-.117-1.994zm0-5.993H10l-.117.007A1 1 0 0 0 10 13h11.003l.117-.007A1 1 0 0 0 21.003 11zm0-6H10l-.117.007A1 1 0 0 0 10 7h11.003l.117-.007A1 1 0 0 0 21.003 5zM6.707 16.293a1 1 0 0 0-1.414 0L4 17.586l-.293-.293a1 1 0 0 0-1.414 1.414l1 1a1 1 0 0 0 1.414 0l2-2a1 1 0 0 0 0-1.414zm-1.414-6.5a1 1 0 0 1 1.414 1.414l-2 2a1 1 0 0 1-1.414 0l-1-1a1 1 0 1 1 1.414-1.414l.293.293l1.293-1.293z" fill="currentColor"></path></g></svg>',TextAlignLeft24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6a1 1 0 0 1 1-1h15a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm0 12a1 1 0 0 1 1-1h11a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm1-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignCenter24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M4 6a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1zm2 12a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1zm-3-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignRight24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M5 6a1 1 0 0 1 1-1h15a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1zm4 12a1 1 0 0 1 1-1h11a1 1 0 1 1 0 2H10a1 1 0 0 1-1-1zm-6-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignJustify24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm0 12a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm1-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',Search24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M10 2.5a7.5 7.5 0 0 1 5.964 12.048l4.743 4.745a1 1 0 0 1-1.32 1.497l-.094-.083l-4.745-4.743A7.5 7.5 0 1 1 10 2.5zm0 2a5.5 5.5 0 1 0 0 11a5.5 5.5 0 0 0 0-11z" fill="currentColor"></path></g></svg>',Add24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M11.883 3.007L12 3a1 1 0 0 1 .993.883L13 4v7h7a1 1 0 0 1 .993.883L21 12a1 1 0 0 1-.883.993L20 13h-7v7a1 1 0 0 1-.883.993L12 21a1 1 0 0 1-.993-.883L11 20v-7H4a1 1 0 0 1-.993-.883L3 12a1 1 0 0 1 .883-.993L4 11h7V4a1 1 0 0 1 .883-.993L12 3l-.117.007z" fill="currentColor"></path></g></svg>',DocumentEdit16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M8 1v3.5A1.5 1.5 0 0 0 9.498 6h3.5v1.035a2.548 2.548 0 0 0-1.37.712l-4.287 4.287a3.777 3.777 0 0 0-.994 1.755l-.302 1.209H4.5a1.5 1.5 0 0 1-1.5-1.5V2.5A1.5 1.5 0 0 1 4.5 1H8zm4.998 7.06c-.242.071-.47.203-.662.394L8.05 12.74a2.777 2.777 0 0 0-.73 1.29l-.303 1.211a.61.61 0 0 0 .738.74l1.211-.303a2.776 2.776 0 0 0 1.29-.73l4.288-4.288a1.56 1.56 0 0 0-1.545-2.6zm-4-6.81V4.5a.5.5 0 0 0 .5.5h3.25l-3.75-3.75z" fill="currentColor"></path></g></svg>',Star48Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"><path d="M21.803 6.085c.899-1.82 3.495-1.82 4.394 0l4.852 9.832l10.85 1.577c2.01.292 2.813 2.762 1.358 4.179l-7.85 7.653l1.853 10.806c.343 2.002-1.758 3.528-3.555 2.583L24 37.613l-9.705 5.102c-1.797.945-3.898-.581-3.555-2.583l1.854-10.806l-7.851-7.653c-1.455-1.417-.652-3.887 1.357-4.179l10.85-1.577l4.853-9.832z" fill="currentColor"></path></svg>',CommentNote20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.5 1A1.5 1.5 0 0 0 10 2.5v5A1.5 1.5 0 0 0 11.5 9h6A1.5 1.5 0 0 0 19 7.5v-5A1.5 1.5 0 0 0 17.5 1h-6zm1 5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1 0-1zM12 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5zM4.6 3H9v4.5a2.5 2.5 0 0 0 2.5 2.5h6c.171 0 .338-.017.5-.05v2.326c0 1.418-1.164 2.566-2.6 2.566h-4.59l-4.011 2.961a1.009 1.009 0 0 1-1.4-.199a.978.978 0 0 1-.199-.59v-2.172h-.6c-1.436 0-2.6-1.149-2.6-2.566v-6.71C2 4.149 3.164 3 4.6 3z" fill="currentColor"></path></g></svg>',ArrowRight20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.265 3.205a.75.75 0 0 0-1.03 1.09l5.239 4.955H2.75a.75.75 0 0 0 0 1.5h12.726l-5.241 4.957a.75.75 0 1 0 1.03 1.09l6.418-6.07a.995.995 0 0 0 .3-.566a.753.753 0 0 0-.002-.329a.995.995 0 0 0-.298-.557l-6.418-6.07z" fill="currentColor"></path></g></svg>',LinkOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0c-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0c59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z" fill="currentColor"></path></svg>',RollbackOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M793 242H366v-74c0-6.7-7.7-10.4-12.9-6.3l-142 112a8 8 0 0 0 0 12.6l142 112c5.2 4.1 12.9.4 12.9-6.3v-74h415v470H175c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h618c35.3 0 64-28.7 64-64V306c0-35.3-28.7-64-64-64z" fill="currentColor"></path></svg>',LikeOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M885.9 533.7c16.8-22.2 26.1-49.4 26.1-77.7c0-44.9-25.1-87.4-65.5-111.1a67.67 67.67 0 0 0-34.3-9.3H572.4l6-122.9c1.4-29.7-9.1-57.9-29.5-79.4A106.62 106.62 0 0 0 471 99.9c-52 0-98 35-111.8 85.1l-85.9 311H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h601.3c9.2 0 18.2-1.8 26.5-5.4c47.6-20.3 78.3-66.8 78.3-118.4c0-12.6-1.8-25-5.4-37c16.8-22.2 26.1-49.4 26.1-77.7c0-12.6-1.8-25-5.4-37c16.8-22.2 26.1-49.4 26.1-77.7c-.2-12.6-2-25.1-5.6-37.1zM184 852V568h81v284h-81zm636.4-353l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 22.4-13.2 42.6-33.6 51.8H329V564.8l99.5-360.5a44.1 44.1 0 0 1 42.2-32.3c7.6 0 15.1 2.2 21.1 6.7c9.9 7.4 15.2 18.6 14.6 30.5l-9.6 198.4h314.4C829 418.5 840 436.9 840 456c0 16.5-7.2 32.1-19.6 43z" fill="currentColor"></path></svg>',DislikeOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M885.9 490.3c3.6-12 5.4-24.4 5.4-37c0-28.3-9.3-55.5-26.1-77.7c3.6-12 5.4-24.4 5.4-37c0-28.3-9.3-55.5-26.1-77.7c3.6-12 5.4-24.4 5.4-37c0-51.6-30.7-98.1-78.3-118.4a66.1 66.1 0 0 0-26.5-5.4H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h129.3l85.8 310.8C372.9 889 418.9 924 470.9 924c29.7 0 57.4-11.8 77.9-33.4c20.5-21.5 31-49.7 29.5-79.4l-6-122.9h239.9c12.1 0 23.9-3.2 34.3-9.3c40.4-23.5 65.5-66.1 65.5-111c0-28.3-9.3-55.5-26.1-77.7zM184 456V172h81v284h-81zm627.2 160.4H496.8l9.6 198.4c.6 11.9-4.7 23.1-14.6 30.5c-6.1 4.5-13.6 6.8-21.1 6.7a44.28 44.28 0 0 1-42.2-32.3L329 459.2V172h415.4a56.85 56.85 0 0 1 33.6 51.8c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4l21.9 19a56.76 56.76 0 0 1 19.6 43c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4l21.9 19a56.76 56.76 0 0 1 19.6 43c0 9.7-2.3 18.9-6.9 27.3l-14 25.5l21.9 19a56.76 56.76 0 0 1 19.6 43c0 19.1-11 37.5-28.8 48.4z" fill="currentColor"></path></svg>',CommentOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><defs></defs><path d="M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="currentColor"></path><path d="M894 345c-48.1-66-115.3-110.1-189-130v.1c-17.1-19-36.4-36.5-58-52.1c-163.7-119-393.5-82.7-513 81c-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4c5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6c17.7 0 32-14.3 32-32V753c88.1-119.6 90.4-284.9 1-408zM323 735l-12-5l-99 31l-1-104l-8-9c-84.6-103.2-90.2-251.9-11-361c96.4-132.2 281.2-161.4 413-66c132.2 96.1 161.5 280.6 66 412c-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10l1 104l-98-33l-12 5c-56 20.8-115.7 22.5-171 7l-.2-.1C613.7 788.2 680.7 742.2 729 676c76.4-105.3 88.8-237.6 44.4-350.4l.6.4c23 16.5 44.1 37.1 62 62c72.6 99.6 68.5 235.2-8 330z" fill="currentColor"></path><path d="M433 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="currentColor"></path></svg>',IosCode:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g fill="currentColor"><path d="M332,142.7c-1.2-1.1-2.7-1.7-4.1-1.7s-3,0.6-4.1,1.7l-13.8,13.2c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l95.8,91.5l-95.8,91.5c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l13.8,13.2c1.2,1.1,2.6,1.7,4.1,1.7c1.5,0,3-0.6,4.1-1.7l114.2-109c1.2-1.1,1.9-2.7,1.9-4.3c0-1.6-0.7-3.2-1.9-4.3L332,142.7z"></path><path d="M204,160.2c0-1.6-0.7-3.2-1.9-4.3l-13.8-13.2c-1.2-1.1-2.7-1.7-4.1-1.7s-3,0.6-4.1,1.7l-114.2,109c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l114.2,109c1.2,1.1,2.7,1.7,4.1,1.7c1.5,0,3-0.6,4.1-1.7l13.8-13.2c1.2-1.1,1.9-2.7,1.9-4.3c0-1.6-0.7-3.2-1.9-4.3L106.3,256l95.8-91.5C203.3,163.4,204,161.8,204,160.2z"></path></g></svg>',IosNotificationsOutline:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g fill="currentColor"><path d="M289.7,403c-6.1,0-11.4,4.2-12.7,10.2c-1,4.5-2.7,8.2-5,10.9c-1.3,1.5-5.1,5.9-16.1,5.9c-11,0-14.8-4.5-16.1-5.9c-2.3-2.7-4-6.4-5-10.9c-1.3-6-6.6-10.2-12.7-10.2h0c-8.4,0-14.5,7.8-12.7,15.9c5,22.3,21,37.1,46.5,37.1s41.5-14.7,46.5-37.1C304.2,410.8,298,403,289.7,403L289.7,403z"></path><path d="M412,352.2c-15.4-20.3-45.7-32.2-45.7-123.1c0-93.3-41.2-130.8-79.6-139.8c-3.6-0.9-6.2-2.1-6.2-5.9v-2.9c0-13.3-10.8-24.6-24-24.6c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-13.2,0-24,11.3-24,24.6v2.9c0,3.7-2.6,5-6.2,5.9c-38.5,9.1-79.6,46.5-79.6,139.8c0,90.9-30.3,102.7-45.7,123.1c-9.9,13.1-0.5,31.8,15.9,31.8h140.4h139.7C412.5,384,421.8,365.2,412,352.2z M373,358H139.8c-3.8,0-5.8-4.4-3.3-7.3c7-8,14.7-18.5,21-33.4c9.6-22.6,14.3-51.5,14.3-88.2c0-37.3,7-66.5,20.9-86.8c12.4-18.2,27.9-25.1,38.7-27.6c8.4-2,14.4-5.8,18.6-10.5c3.2-3.6,8.7-3.8,11.9-0.2c5.1,5.7,12,9.1,18.8,10.7c10.8,2.5,26.3,9.4,38.7,27.6c13.9,20.3,20.9,49.5,20.9,86.8c0,36.7,4.7,65.6,14.3,88.2c6.5,15.2,14.4,25.9,21.5,33.9C378.3,353.9,376.5,358,373,358z"></path></g></svg>',IosNotificationsOff:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g fill="currentColor"><path d="M255.9,456c31.1,0,48.1-22,48.1-53h-96.3C207.7,434,224.7,456,255.9,456z"></path><path d="M154.5,55c-2.5-4.3-7-6.8-11.6-7c0.1,0,0.2,0,0.3,0c-0.3,0-0.6,0-0.9,0c-0.1,0-0.2,0-0.3,0c-2.3,0-4.7,0.7-6.9,1.9c-6.8,3.9-9.1,12.6-5.1,19.3L357.5,457c2.6,4.5,7.4,7,12.3,7c2.4,0,4.9-0.6,7.2-1.9c6.8-3.9,9.1-12.6,5.1-19.3L154.5,55z"></path><path d="M296.1,384L159,150.5c-8.2,20.2-13.3,46-13.3,78.6c0,90.9-30.3,102.7-45.7,123.1c-9.9,13.1-0.5,31.8,15.9,31.8l140.4,0H296.1z"></path><path d="M412,352.2c-15.4-20.3-45.7-32.2-45.7-123.1c0-93.3-41.2-130.8-79.6-139.8c-3.6-0.9-6.2-2.1-6.2-5.9v-2.9c0-13.4-11-24.7-24.4-24.6c-13.4-0.2-24.4,11.2-24.4,24.6v2.9c0,3.7-2.6,5-6.2,5.9c-8.7,2-17.5,5.5-25.9,10.8L366.1,384h29.9C412.5,384,421.9,365.2,412,352.2z"></path></g></svg>',IosClose:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><path fill="currentColor" d="M278.6,256l68.2-68.2c6.2-6.2,6.2-16.4,0-22.6c-6.2-6.2-16.4-6.2-22.6,0L256,233.4l-68.2-68.2c-6.2-6.2-16.4-6.2-22.6,0c-3.1,3.1-4.7,7.2-4.7,11.3c0,4.1,1.6,8.2,4.7,11.3l68.2,68.2l-68.2,68.2c-3.1,3.1-4.7,7.2-4.7,11.3c0,4.1,1.6,8.2,4.7,11.3c6.2,6.2,16.4,6.2,22.6,0l68.2-68.2l68.2,68.2c6.2,6.2,16.4,6.2,22.6,0c6.2-6.2,6.2-16.4,0-22.6L278.6,256z"></path></svg>',LockOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z" fill="currentColor"></path></svg>',UnlockOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M832 464H332V240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v68c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-68c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zm-40 376H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z" fill="currentColor"></path></svg>',TextColor24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M17.75 14.5A2.25 2.25 0 0 1 20 16.75v3A2.25 2.25 0 0 1 17.75 22H5.25A2.25 2.25 0 0 1 3 19.75v-3a2.25 2.25 0 0 1 2.25-2.25h12.5zm0 1.5H5.25a.75.75 0 0 0-.75.75v3c0 .415.336.75.75.75h12.5a.75.75 0 0 0 .75-.75v-3a.75.75 0 0 0-.75-.75zM7.053 11.97l3.753-9.496c.236-.595 1.043-.63 1.345-.104l.05.105l3.747 9.5a.75.75 0 0 1-1.352.643l-.044-.092L13.556 10H9.443l-.996 2.52a.75.75 0 0 1-.876.454l-.097-.031a.75.75 0 0 1-.453-.876l.032-.098l3.753-9.495l-3.753 9.495zm4.45-7.178L10.036 8.5h2.928l-1.461-3.708z" fill="currentColor"></path></g></svg>',Color24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M3.839 5.858c2.94-3.916 9.03-5.055 13.364-2.36c4.28 2.66 5.854 7.777 4.1 12.577c-1.655 4.533-6.016 6.328-9.159 4.048c-1.177-.854-1.634-1.925-1.854-3.664l-.106-.987l-.045-.398c-.123-.934-.311-1.352-.705-1.572c-.535-.298-.892-.305-1.595-.033l-.351.146l-.179.078c-1.014.44-1.688.595-2.541.416l-.2-.047l-.164-.047c-2.789-.864-3.202-4.647-.565-8.157zm.984 6.716l.123.037l.134.03c.439.087.814.015 1.437-.242l.602-.257c1.202-.493 1.985-.54 3.046.05c.917.512 1.275 1.298 1.457 2.66l.053.459l.055.532l.047.422c.172 1.361.485 2.09 1.248 2.644c2.275 1.65 5.534.309 6.87-3.349c1.516-4.152.174-8.514-3.484-10.789c-3.675-2.284-8.899-1.306-11.373 1.987c-2.075 2.763-1.82 5.28-.215 5.816zm11.225-1.994a1.25 1.25 0 1 1 2.414-.647a1.25 1.25 0 0 1-2.414.647zm.494 3.488a1.25 1.25 0 1 1 2.415-.647a1.25 1.25 0 0 1-2.415.647zM14.07 7.577a1.25 1.25 0 1 1 2.415-.647a1.25 1.25 0 0 1-2.415.647zm-.028 8.998a1.25 1.25 0 1 1 2.414-.647a1.25 1.25 0 0 1-2.414.647zm-3.497-9.97a1.25 1.25 0 1 1 2.415-.646a1.25 1.25 0 0 1-2.415.646z" fill="currentColor"></path></g></svg>',FormatPainterOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><defs></defs><path d="M840 192h-56v-72c0-13.3-10.7-24-24-24H168c-13.3 0-24 10.7-24 24v272c0 13.3 10.7 24 24 24h592c13.3 0 24-10.7 24-24V256h32v200H465c-22.1 0-40 17.9-40 40v136h-44c-4.4 0-8 3.6-8 8v228c0 .6.1 1.3.2 1.9c-.1 2-.2 4.1-.2 6.1c0 46.4 37.6 84 84 84s84-37.6 84-84c0-2.1-.1-4.1-.2-6.1c.1-.6.2-1.2.2-1.9V640c0-4.4-3.6-8-8-8h-44V520h351c22.1 0 40-17.9 40-40V232c0-22.1-17.9-40-40-40zM720 352H208V160h512v192zM477 876c0 11-9 20-20 20s-20-9-20-20V696h40v180z" fill="currentColor"></path></svg>',Blockquote:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 15h15"></path><path d="M21 19H6"></path><path d="M15 11h6"></path><path d="M21 7h-6"></path><path d="M9 9h1a1 1 0 1 1-1 1V7.5a2 2 0 0 1 2-2"></path><path d="M3 9h1a1 1 0 1 1-1 1V7.5a2 2 0 0 1 2-2"></path></g></svg>',UserMultiple:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M30 30h-2v-5a5.006 5.006 0 0 0-5-5v-2a7.008 7.008 0 0 1 7 7z" fill="currentColor"></path><path d="M22 30h-2v-5a5.006 5.006 0 0 0-5-5H9a5.006 5.006 0 0 0-5 5v5H2v-5a7.008 7.008 0 0 1 7-7h6a7.008 7.008 0 0 1 7 7z" fill="currentColor"></path><path d="M20 2v2a5 5 0 0 1 0 10v2a7 7 0 0 0 0-14z" fill="currentColor"></path><path d="M12 4a5 5 0 1 1-5 5a5 5 0 0 1 5-5m0-2a7 7 0 1 0 7 7a7 7 0 0 0-7-7z" fill="currentColor"></path></svg>',Sun24Filled:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="12" cy="12" r="5" fill="#FFD700" /><path fill="none" d="M12,3V5M12,19V21M21,12H19M5,12H3M18.364,5.636L16.95,7.05M7.05,16.95L5.636,18.364M18.364,18.364L16.95,16.95M7.05,7.05L5.636,5.636" stroke="#FFD700" stroke-width="2" stroke-linecap="round" /></svg>',Moon24Filled:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M283.211 512c78.962 0 151.079-35.925 198.857-94.792 7.068-8.708-.639-21.43-11.562-19.35-124.203 23.654-238.262-71.576-238.262-196.954 0-72.222 38.662-138.635 101.498-174.394 9.686-5.512 7.25-20.197-3.756-22.23A258.156 258.156 0 0 0 283.211 0c-141.309 0-256 114.511-256 256 0 141.309 114.511 256 256 256z" /></svg>',Search24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M10 2.75a7.25 7.25 0 0 1 5.63 11.819l4.9 4.9a.75.75 0 0 1-.976 1.134l-.084-.073l-4.901-4.9A7.25 7.25 0 1 1 10 2.75zm0 1.5a5.75 5.75 0 1 0 0 11.5a5.75 5.75 0 0 0 0-11.5z" fill="currentColor"></path></g></svg>',Add24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M11.75 3a.75.75 0 0 1 .743.648l.007.102l.001 7.25h7.253a.75.75 0 0 1 .102 1.493l-.102.007h-7.253l.002 7.25a.75.75 0 0 1-1.493.101l-.007-.102l-.002-7.249H3.752a.75.75 0 0 1-.102-1.493L3.752 11h7.25L11 3.75a.75.75 0 0 1 .75-.75z" fill="currentColor"></path></g></svg>',ArrowReplyDown24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M9.704 8.706A1 1 0 1 0 8.29 7.292l-4.997 5.004a1 1 0 0 0 0 1.413l4.997 4.998a1 1 0 1 0 1.415-1.414L6.41 14H13a8 8 0 0 0 7.996-7.75L21 6a1 1 0 1 0-2 0a6 6 0 0 1-5.775 5.996L13 12H6.414l3.29-3.294z" fill="currentColor"></path></g></svg>',SendAltFilled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M27.71 4.29a1 1 0 0 0-1.05-.23l-22 8a1 1 0 0 0 0 1.87l8.59 3.43L19.59 11L21 12.41l-6.37 6.37l3.44 8.59A1 1 0 0 0 19 28a1 1 0 0 0 .92-.66l8-22a1 1 0 0 0-.21-1.05z" fill="currentColor"></path></svg>',DocumentDownload:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M30 25l-1.414-1.414L26 26.172V18h-2v8.172l-2.586-2.586L20 25l5 5l5-5z" fill="currentColor"></path><path d="M18 28H8V4h8v6a2.006 2.006 0 0 0 2 2h6v3h2v-5a.91.91 0 0 0-.3-.7l-7-7A.909.909 0 0 0 18 2H8a2.006 2.006 0 0 0-2 2v24a2.006 2.006 0 0 0 2 2h10zm0-23.6l5.6 5.6H18z" fill="currentColor"></path></svg>',FileUpload:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 3v4a1 1 0 0 0 1 1h4"></path><path d="M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2z"></path><path d="M12 11v6"></path><path d="M9 14l3-3l3 3"></path></g></svg>',DocumentEdit16Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M5 1a2 2 0 0 0-2 2v9.998a2 2 0 0 0 2 2h1.046l.25-1H5a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h3v2.5A1.5 1.5 0 0 0 9.498 6h2.5v1.44c.306-.209.647-.344 1-.405V5.413a1.5 1.5 0 0 0-.44-1.06L9.645 1.439A1.5 1.5 0 0 0 8.585 1H5zm6.791 4H9.5a.5.5 0 0 1-.5-.5V2.206l2.792 2.792zm1.207 3.06c-.242.071-.47.203-.662.394L8.05 12.74a2.777 2.777 0 0 0-.722 1.257l-.009.033l-.302 1.211a.61.61 0 0 0 .738.74l1.211-.303a2.776 2.776 0 0 0 1.29-.73l4.288-4.288a1.56 1.56 0 0 0-1.545-2.6z" fill="currentColor"></path></g></svg>',Star48Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"><path d="M21.803 6.085c.899-1.82 3.495-1.82 4.394 0l4.852 9.832l10.85 1.577c2.01.292 2.813 2.762 1.358 4.179l-7.85 7.653l1.853 10.806c.343 2.002-1.758 3.528-3.555 2.583L24 37.613l-9.705 5.102c-1.797.945-3.898-.581-3.555-2.583l1.854-10.806l-7.851-7.653c-1.455-1.417-.652-3.887 1.357-4.179l10.85-1.577l4.853-9.832zM24 7.283l-4.82 9.764a2.45 2.45 0 0 1-1.844 1.34L6.56 19.954l7.798 7.601a2.45 2.45 0 0 1 .704 2.169l-1.84 10.732l9.638-5.067a2.45 2.45 0 0 1 2.28 0l9.638 5.067l-1.84-10.732a2.45 2.45 0 0 1 .704-2.169l7.798-7.6l-10.776-1.566a2.45 2.45 0 0 1-1.845-1.34L24 7.282z" fill="currentColor"></path></svg>',CommentNote20Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.5 1A1.5 1.5 0 0 0 10 2.5v5A1.5 1.5 0 0 0 11.5 9h6A1.5 1.5 0 0 0 19 7.5v-5A1.5 1.5 0 0 0 17.5 1h-6zm1 5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1 0-1zM12 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5zM4.6 3H9v1H4.6C3.704 4 3 4.713 3 5.566v6.71c0 .853.704 1.566 1.6 1.566h1.6V17h.003l.002-.001l4.276-3.157H15.4c.896 0 1.6-.713 1.6-1.566V10h.5c.171 0 .338-.017.5-.05v2.326c0 1.418-1.164 2.566-2.6 2.566h-4.59l-4.011 2.961a1.009 1.009 0 0 1-1.4-.199a.978.978 0 0 1-.199-.59v-2.172h-.6c-1.436 0-2.6-1.149-2.6-2.566v-6.71C2 4.149 3.164 3 4.6 3z" fill="currentColor"></path></g></svg>',VideoClip24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M9.5 9.385v5.231c0 .57.61.931 1.11.658l4.786-2.616a.75.75 0 0 0 0-1.316L10.61 8.726a.75.75 0 0 0-1.11.659zM5.25 3A3.25 3.25 0 0 0 2 6.25v11.5A3.25 3.25 0 0 0 5.25 21h13.5A3.25 3.25 0 0 0 22 17.75V6.25A3.25 3.25 0 0 0 18.75 3H5.25zM3.5 6.25c0-.966.784-1.75 1.75-1.75h13.5c.966 0 1.75.784 1.75 1.75v11.5a1.75 1.75 0 0 1-1.75 1.75H5.25a1.75 1.75 0 0 1-1.75-1.75V6.25z" fill="currentColor"></path></g></svg>',Italic:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 5h6"></path><path d="M7 19h6"></path><path d="M14 5l-4 14"></path></g></svg>'};function Tl(e){return i({name:`Icon${e}`,props:{size:{type:[String,Number],default:20},color:{type:String,default:"var(--black)"}},setup:t=>()=>{const a=Sl[e];if(!a)return R("span",{style:{color:"red"}},"?");const l="number"==typeof t.size?`${t.size}px`:t.size,n=function(e,t){const a=(new DOMParser).parseFromString(e,"image/svg+xml").documentElement;if("svg"!==a.tagName)return R("span",{style:{color:"red"}},"?");const l="number"==typeof t.size?`${t.size}px`:t.size;return function e(t){const a=t.tagName.toLowerCase(),n={};for(let l=0;l<t.attributes.length;l++){const e=t.attributes[l];let a=e.value;"fill"===e.name&&"none"===a&&(a="none"),n[e.name]=a}"svg"===a&&(n.width=l,n.height=l,delete n.width,delete n.height,n.width=l,n.height=l);const i=[];for(let l=0;l<t.children.length;l++)i.push(e(t.children[l]));return t.textContent&&0===t.children.length?R(a,n,t.textContent.trim()):R(a,n,i)}(a)}(a,t);return R("span",{class:"icon-wrapper",style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:l,height:l,lineHeight:1,color:t.color,transition:"color 0.3s ease"}},[n])}})}const Ml=Tl("TextHeader120Filled"),Rl=Tl("TextHeader220Filled"),Al=Tl("TextHeader320Filled"),El=Tl("TextBold20Filled"),_l=Tl("TextUnderline24Filled"),Il=Tl("TextStrikethrough20Filled"),zl=Tl("Code20Filled"),Pl=Tl("Image28Regular"),Bl=Tl("ArrowUndo16Filled"),$l=Tl("ArrowRedo16Filled"),Dl=Tl("LineHorizontal120Filled"),Ul=Tl("FullScreenMaximize16Filled"),Vl=Tl("ResizeSmall20Filled"),Fl=Tl("TextBulletListLtr16Filled"),Ol=Tl("TextNumberListLtr16Filled"),Hl=Tl("TaskListLtr24Filled"),jl=Tl("TextAlignLeft24Filled"),Nl=Tl("TextAlignCenter24Filled"),ql=Tl("TextAlignRight24Filled"),Yl=Tl("TextAlignJustify24Filled"),Wl=Tl("DocumentEdit16Filled"),Jl=Tl("ArrowRight20Filled"),Kl=Tl("LinkOutlined"),Xl=Tl("RollbackOutlined"),Gl=Tl("LikeOutlined"),Zl=Tl("DislikeOutlined"),Ql=Tl("CommentOutlined"),en=Tl("IosCode"),tn=Tl("IosNotificationsOutline"),an=Tl("IosNotificationsOff"),ln=Tl("IosClose"),nn=Tl("LockOutlined"),on=Tl("UnlockOutlined"),rn=Tl("TextColor24Regular"),sn=Tl("Color24Regular"),cn=Tl("FormatPainterOutlined"),un=Tl("Blockquote"),dn=Tl("UserMultiple"),mn=Tl("Sun24Filled"),vn=Tl("Moon24Filled"),pn=Tl("Search24Regular"),gn=Tl("Add24Regular"),hn=Tl("ArrowReplyDown24Filled"),fn=Tl("SendAltFilled"),wn=Tl("DocumentDownload"),yn=Tl("FileUpload"),bn=Tl("DocumentEdit16Regular"),kn=Tl("Star48Regular"),xn=Tl("CommentNote20Regular"),Cn=Tl("VideoClip24Regular"),Ln=Tl("Italic"),Sn=Sl.TextHeader120Filled,Tn=Sl.TextHeader220Filled,Mn=Sl.TextHeader320Filled,Rn=Sl.TextBold20Filled,An=Sl.TextUnderline24Filled,En=Sl.TextStrikethrough20Filled,_n=Sl.Code20Filled,In=Sl.Image28Regular,zn=Sl.LineHorizontal120Filled,Pn=Sl.TextBulletListLtr16Filled,Bn=Sl.TextNumberListLtr16Filled,$n=Sl.TaskListLtr24Filled,Dn=Sl.LinkOutlined,Un=Sl.Blockquote,Vn=Sl.VideoClip24Regular,Fn=Sl.Italic,On=(e,t)=>[{id:"heading1",name:"标题 1",icon:Sn,keywords:"h1 标题 heading",shortcut:"Ctrl+Alt+1",action:e=>e.chain().focus().toggleHeading({level:1}).run()},{id:"heading2",name:"标题 2",icon:Tn,keywords:"h2 标题 heading",shortcut:"Ctrl+Alt+2",action:e=>e.chain().focus().toggleHeading({level:2}).run()},{id:"heading3",name:"标题 3",icon:Mn,keywords:"h3 标题 heading",shortcut:"Ctrl+Alt+3",action:e=>e.chain().focus().toggleHeading({level:3}).run()},"|",{id:"bold",name:"加粗",icon:Rn,keywords:"bold 加粗 粗体 b",shortcut:"Ctrl+B",action:e=>e.chain().focus().toggleBold().run()},{id:"italic",name:"斜体",icon:Fn,keywords:"italic 斜体 i",shortcut:"Ctrl+I",action:e=>e.chain().focus().toggleItalic().run()},{id:"strike",name:"删除线",icon:En,keywords:"strike 删除线 strikethrough",action:e=>e.chain().focus().toggleStrike().run()},{id:"underline",name:"下划线",icon:An,keywords:"underline 下划线 u",shortcut:"Ctrl+U",action:e=>e.chain().focus().toggleUnderline().run()},{id:"code",name:"行内代码",icon:_n,keywords:"code 代码 行内代码 inline",shortcut:"Ctrl+E",action:e=>e.chain().focus().toggleCode().run()},"|",{id:"bulletList",name:"无序列表",icon:Pn,keywords:"ul 列表 list bullet",action:e=>e.chain().focus().toggleBulletList().run()},{id:"orderedList",name:"有序列表",icon:Bn,keywords:"ol 列表 list ordered number",action:e=>e.chain().focus().toggleOrderedList().run()},{id:"taskList",name:"任务列表",icon:$n,keywords:"todo 任务 task checklist",action:e=>e.chain().focus().toggleTaskList().run()},"|",{id:"blockquote",name:"引用",icon:Un,keywords:"quote 引用 blockquote",action:e=>e.chain().focus().toggleBlockquote().run()},{id:"codeBlock",name:"代码块",icon:_n,keywords:"code 代码 codeblock",shortcut:"Ctrl+Alt+C",action:e=>e.chain().focus().toggleCodeBlock().run()},"|",{id:"image",name:"图片",icon:In,keywords:"image 图片 img picture",action:e=>{}},{id:"link",name:"链接",icon:Dn,keywords:"link 链接 url",action:e=>{t&&t("插入链接",(()=>{}),!1)}},{id:"bilibili",name:"B站视频",icon:Vn,keywords:"bilibili b站 视频 video",action:e=>{t&&t("插入bilibili视频链接",(()=>{}),!0)}},{id:"horizontalRule",name:"分割线",icon:zn,keywords:"hr 分割线 divider line",action:e=>e.chain().focus().setHorizontalRule().run()}],Hn=ot.extend({addNodeView(){return e=>{const t=document.createElement("li");t.setAttribute("data-type","taskItem"),t.dataset.checked=e.node.attrs.checked;const a=document.createElement("label");a.className="cst-task-label",a.style.display="flex",a.style.alignItems="flex-start",a.style.marginTop="0.2rem";const l=document.createElement("input");l.type="checkbox",l.checked=e.node.attrs.checked,l.style.display="none",l.className="cst-task-checkbox";const n=document.createElement("span");n.className="cst-task-checkbox-wrapper",n.style.display="inline-flex",n.style.justifyContent="center",n.style.alignItems="center",n.style.width="1rem",n.style.height="1rem",n.style.border="1px solid #ccc",n.style.borderRadius="0.25rem",n.style.marginRight="0.5rem",n.style.position="relative",n.style.cursor=e.editor.isEditable?"pointer":"default",n.style.transition=e.editor.isEditable?"background-color 0.2s ease":"none",n.style.flexShrink="0",n.style.marginTop="0.2rem",e.editor.isEditable||(n.style.cursor="not-allowed",n.setAttribute("aria-disabled","true"),n.title="只读模式下不可更改",a.style.transition="none",a.style.transform="none");const i=document.createElement("span");function o(){n.style.backgroundColor=l.checked?"var(--purple-contrast)":"white",i.style.opacity=l.checked?"1":"0",!e.editor.isEditable&&l.checked&&(n.style.backgroundColor="var(--purple-contrast-disabled, #a095c3)")}i.className="cst-task-checkmark",i.textContent="✓",i.style.color="white",i.style.fontSize="0.75rem",i.style.opacity=l.checked?"1":"0",i.style.transition=e.editor.isEditable?"opacity 0.2s ease":"none",o(),n.appendChild(i),n.addEventListener("mousedown",(a=>{if(a.preventDefault(),a.stopPropagation(),e.editor.isEditable)l.checked=!l.checked,o(),"function"==typeof e.getPos&&e.editor.commands.command((({tr:t})=>(t.setNodeMarkup(e.getPos(),void 0,{...e.node.attrs,checked:l.checked}),!0))),t.dataset.checked=l.checked.toString();else if(this.options.onReadOnlyChecked){const a=!l.checked;if(!1===this.options.onReadOnlyChecked(e.node,a))return;l.checked=a,o(),"function"==typeof e.getPos&&e.editor.commands.command((({tr:t})=>(t.setNodeMarkup(e.getPos(),void 0,{...e.node.attrs,checked:a}),!0))),t.dataset.checked=a.toString()}}));const r=document.createElement("div");return r.className="cst-task-content",r.style.flex="1",a.appendChild(l),a.appendChild(n),t.appendChild(a),t.appendChild(r),{dom:t,contentDOM:r}}}}),jn=Yt(Wt),Nn=[["document",rt],["paragraph",st],["text",ct],["image",wl],["dropcursor",ut],["bold",dt],["italic",mt],["strike",vt],["underline",pt],["code",gt],["heading",ht],["bulletList",ft.configure({keepMarks:!0})],["orderedList",wt.configure({keepMarks:!0})],["listItem",yt],["taskList",bt.configure({itemTypeName:"taskItem"})],["taskItem",Hn.configure({nested:!0,onReadOnlyChecked:(e,t)=>!1})],["blockquote",kt],["textStyle",xt],["color",Ct.configure({types:["textStyle"]})],["backgroundColor",Lt.configure({multicolor:!0})],["codeBlockLowlight",Qa.configure({lowlight:jn})],["horizontalRule",St],["link",Tt.configure({defaultProtocol:"https"})],["history",Mt],["typography",Rt],["markdown",Vt.configure({transformPastedText:!0})],["focus",At.configure({mode:"deepest"})],["gapcursor",Et],["mention",xl],["bilibili",Na],["floatingMenu",_t],["bubbleMenu",It],["align",zt.configure({types:["heading","paragraph","blockquote"],alignments:["left","center","right","justify"],defaultAlignment:"left"})],["fullscreen",el],["slashMenu",Ll.configure({items:On()})]];const qn={extensionMap:new Map(Nn),replaceImageUrls:Da.replaceImageUrls,toJsonString:Da.toJsonString,toJsonObject:Da.toJsonObject,serializeContent:Da.serializeContent},Yn={URL:"/core/articles",search:async(e,t)=>(await La(Yn.URL,e,{signal:t})).data,title:async e=>(await La(Yn.URL+"/"+e+"/title")).data,detail:async e=>(await La(Yn.URL+"/"+e)).data,save:async e=>(await Sa(Yn.URL,e)).data,edit:async e=>(await Ma(Yn.URL+"/"+(null==e?void 0:e.id),e)).data,togglePublishedScope:async e=>(await Aa(`${Yn.URL}/${e}/published-scope`)).data,getHotTags:async(e=5)=>(await La(`${Yn.URL}/hot-tags`,{limit:e})).data,md:async(e,t)=>{try{const a=await La(Yn.URL+"/"+e+"/file");if(a&&a.data){const l=a.data.split(/\r?\n/),n=l.pop()||"";t.setContent(qn.toJsonObject(n));const i=t.getMarkdown(),o=new Blob([l.join("\n")+"\n"+i],{type:"text/markdown"}),r=window.URL.createObjectURL(o),s=document.createElement("a");s.href=r;const c=a.headers["content-disposition"];let u=decodeURIComponent((null==c?void 0:c.split("filename=")[1])||`article-${e}`);u+=".md",document.body.appendChild(s),s.download=u,s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(r)}}catch(a){throw a}},delete:async e=>(await Ea(`${Yn.URL}/${e}`)).data},Wn=a("article",{state:()=>({id:""}),actions:{setId(e){this.id=e}},getters:{getId:e=>e.id}}),Jn=a("comment",{state:()=>({id:""}),actions:{setId(e){this.id=e}},getters:{getId:e=>e.id}}),Kn=at.create({name:"characterCount",addOptions:()=>({limit:void 0}),addStorage:()=>({characters:0}),addCommands:()=>({getCharacterCount:()=>({editor:e})=>(e.storage.characterCount.characters,!0)}),addProseMirrorPlugins(){const e=this;return[new Ot({key:new Ht("characterCount"),view:()=>({update:t=>{const{doc:a}=t.state,l=(e=>{let t=0;return e.descendants((e=>{var a;e.isText?t+=(null==(a=e.text)?void 0:a.length)||0:"image"===e.type.name?t+=100:"bilibili"===e.type.name&&(t+=800)})),t})(a);e.storage.characters=l}}),props:{handleKeyDown:(t,a)=>{if(!e.options.limit)return!1;if(e.storage.characters<e.options.limit)return!1;return!(e=>{const t=e.ctrlKey&&"a"===e.key.toLowerCase()||e.ctrlKey&&"c"===e.key.toLowerCase()||e.ctrlKey&&"x"===e.key.toLowerCase()||e.ctrlKey&&"z"===e.key.toLowerCase()||e.ctrlKey&&"y"===e.key.toLowerCase()||e.shiftKey&&["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End"].includes(e.key);return["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End","Tab","Escape","Enter"].includes(e.key)||t})(a)&&(a.preventDefault(),!0)},handlePaste:(t,a)=>{if(!e.options.limit)return!1;const l=e.storage.characters;if(l>=e.options.limit)return a.preventDefault(),!0;const n=a.clipboardData;if(!n)return!1;const i=n.getData("text/plain");if(!i)return!1;return l+i.length>e.options.limit&&(a.preventDefault(),!0)},handleTextInput:(t,a,l,n)=>{if(!e.options.limit)return!1;const i=e.storage.characters;if(i>=e.options.limit)return!0;return i+n.length-(l-a)>e.options.limit}},appendTransaction:(t,a,l)=>{if(!e.options.limit)return null;if(e.storage.characters>=e.options.limit){const e=l.tr;let t=!1;if(e.steps.forEach((e=>{if(e instanceof Object&&"from"in e&&"to"in e&&"slice"in e){const a=e,l=a.to-a.from;a.slice.content.size>l&&(t=!0)}})),t)return null}return null}})]}}),Xn=at.create({name:"formatPainter",addOptions:()=>({enabled:!0}),addStorage:()=>({formatPainter:{isActive:!1,sourceMarks:null,sourceNode:null}}),addCommands:()=>({toggleFormatPainter:()=>({editor:e,state:t})=>{const{formatPainter:a}=e.storage,{selection:l}=t,{from:n,to:i}=l;if(a.isActive){if(a.sourceMarks){const l=t.tr;t.doc.nodesBetween(n,i,((e,t)=>{e.isText&&l.removeMark(t,t+e.nodeSize,null)})),a.sourceMarks.forEach((e=>{l.addMark(n,i,e)})),e.view.dispatch(l)}return a.isActive=!1,a.sourceNode=null,a.sourceMarks=null,!0}{const e=t.doc.nodeAt(n);return!!e&&(a.isActive=!0,a.sourceNode=e,a.sourceMarks=e.marks,!0)}}}),addProseMirrorPlugins:()=>[new Ot({key:new Ht("formatPainter"),props:{handleClick:(e,t)=>{const a=e.editor;if(!a)return!1;const{formatPainter:l}=a.storage;return!!(null==l?void 0:l.isActive)&&(a.commands.toggleFormatPainter(),!0)}}})]}),Gn=(e,t,a,l)=>((e,t,a,l)=>{const i=n(),o=Pt.configure({placeholder:e.placeholder}),r=Kn.configure({limit:e.characterLimit}),c=Xn.configure({enabled:!0});return{editor:i,initEditor:()=>{var n;qn.extensionMap.set("placeholder",o),qn.extensionMap.set("characterCount",r),qn.extensionMap.set("formatPainter",c),void 0!==e.useThumbnail&&qn.extensionMap.set("image",fl(e.useThumbnail)),(a||l)&&qn.extensionMap.set("slashMenu",Ll.configure({items:On(0,l),imageUploadTrigger:a,modalTrigger:l}));const s=[];let u=new Set(["document","paragraph","text",...e.extensions]);return e.allExtensions?(s.push(...qn.extensionMap.values()),u=new Set([...qn.extensionMap.keys()])):u.forEach((e=>{const t=qn.extensionMap.get(e);t?s.push(t):xa.warning(`Unknown extension: ${e}`)})),ta.debug("extensions: ",s),i.value=new Bt({extensions:s,content:e.modelValue&&Object.keys(e.modelValue||{}).length?e.modelValue:"",editable:e.editable,editorProps:e.editorProps}),null==(n=i.value)||n.on("update",(()=>{var e;const a=(null==(e=i.value)?void 0:e.getJSON())||null;ta.debug("update content: ",a),t("update:modelValue",a)})),u},watchEditable:()=>{s((()=>e.editable),(e=>{i.value&&(i.value.setEditable(e),requestAnimationFrame((()=>{try{document.querySelectorAll(".cst-task-checkbox-wrapper").forEach((t=>{if(e){t.classList.remove("readonly-checkbox"),t.removeAttribute("aria-disabled"),t.removeAttribute("title"),t.style.cursor="pointer",t.style.transition="background-color 0.2s ease",t.style.transform="";const e=t.closest("label");e&&(e.style.transition="transform 0.15s ease",e.style.transform="",e.style.pointerEvents="")}else{t.classList.add("readonly-checkbox"),t.setAttribute("aria-disabled","true"),t.setAttribute("title","只读模式下不可更改"),t.style.cursor="not-allowed",t.style.transition="none",t.style.transform="none";const e=t.closest("label");e&&(e.style.transition="none",e.style.transform="none")}}))}catch(t){ta.error("Error updating task checkboxes in readonly mode:",t)}})))}))},clearContent:()=>{var e;null==(e=i.value)||e.commands.clearContent()},setContent:e=>{var t;null==(t=i.value)||t.commands.setContent(e)},getMarkdown:()=>{var e,t;return null==(t=null==(e=i.value)?void 0:e.storage.markdown)?void 0:t.getMarkdown()},cleanupEditor:()=>{setTimeout((()=>{var e;null==(e=i.value)||e.destroy()}),1e3)}}})(e,t,a,l),Zn=(e,t,a,l)=>{const{selectBubbleMenu:n,setupEditorEvents:i}=((e,t,a,l)=>{const n=A({image:!1,bilibili:!1});return{selectBubbleMenu:n,setupEditorEvents:()=>{if(!e.value)return;if(e.value.on("update",(()=>{var t;(null==(t=e.value)?void 0:t.isEditable)||Promise.resolve().then((()=>{try{document.querySelectorAll(".ProseMirror-selectednode").forEach((e=>{e.classList.remove("ProseMirror-selectednode")}))}catch(e){ta.error("Error removing selection in readonly mode:",e)}}))})),!e.value.isEditable){const t=e.value.view.dom,a=e=>{(e.ctrlKey||e.metaKey)&&(e.preventDefault(),e.stopPropagation())};t.addEventListener("mousedown",a,!0);const l=()=>{t.removeEventListener("mousedown",a,!0)};t.dataset.cleanupRegistered||(t.dataset.cleanupRegistered="true",e.value.on("destroy",l))}e.value.on("selectionUpdate",(()=>{var t;const a=null==(t=e.value)?void 0:t.state.selection,l=null==a?void 0:a.node;n.image=!1,n.bilibili=!1,(null==l?void 0:l.type)&&(n.image=!1,n.bilibili="bilibili"===l.type.name,"image"===l.type.name&&ba("tiptap-selection-update",(()=>{const e=document.querySelector(".ProseMirror-selectednode");if(e){const t=e.getBoundingClientRect();t.top>=0&&t.bottom<=window.innerHeight||e.scrollIntoView({behavior:"auto",block:"nearest"})}}),150))})),e.value.on("paste",(n=>{var i,o;if(!e.value)return;const r=(null==(i=n.slice.content.firstChild)?void 0:i.attrs)||{},s=Array.from((null==(o=n.event.clipboardData)?void 0:o.files)||[]);s.some((e=>e.type.startsWith("image/")))&&(ta.debug("Detected image files in clipboard"),t(e.value,s,r,a,l))})),e.value.on("drop",(n=>{var i,o;if(!e.value)return;const r=(null==(i=n.slice.content.firstChild)?void 0:i.attrs)||{},s=Array.from((null==(o=n.event.dataTransfer)?void 0:o.files)||[]);s.some((e=>e.type.startsWith("image/")))&&(ta.debug("Detected image files in drop event"),t(e.value,s,r,a,l))}));let i=null;e.value.on("transaction",(t=>{var a,l;const n=t;if(!(null==(a=e.value)?void 0:a.isEditable)||!(null==(l=n.transaction)?void 0:l.selectionSet))return;const o=e.value.state.selection.from+"-"+e.value.state.selection.to;o!==i&&(i=o,ba("tiptap-image-update",(()=>{var t,a;try{const l=null==(a=null==(t=e.value)?void 0:t.view)?void 0:a.dom;if(!l)return;l.querySelectorAll(".image-wrapper").forEach((e=>{const t=e.classList.contains("ProseMirror-selectednode");e.querySelectorAll(".resize-handle").forEach((e=>{const a=e,l=t;l!==("none"!==a.style.display)&&(l?(a.style.display="block",a.style.visibility="visible",a.style.opacity="1",a.style.pointerEvents="all"):(a.style.display="none",a.style.visibility="hidden",a.style.opacity="0",a.style.pointerEvents="none"))}))}))}catch(l){ta.error("Error updating image handles:",l)}}),100))}))}}})(e,t,a,l);return{selectBubbleMenu:n,setupEditorEvents:i}},Qn={class:"tiptap-btn-wrapper"},ei=Oa(i({__name:"TiptapBtn",props:{show:{type:Boolean,default:!0},trigger:{type:Function,default:()=>{}},icon:{type:Object,required:!0},size:{type:String,default:"20"},isActive:{type:Boolean,default:!1},tooltip:{type:String,default:""},disabled:{type:Boolean,default:!1}},setup:e=>(t,a)=>(m(),d(g(le),{trigger:"hover","theme-overrides":{padding:"4px 8px",textColor:"#333",color:"#fff",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)"}},{trigger:v((()=>[h("div",Qn,[e.show?(m(),d(g(ne),{key:0,class:y(["padding-4",{"is-active":e.isActive}]),quaternary:"",size:"small",onClick:a[0]||(a[0]=t=>e.trigger(t)),disabled:e.disabled},{default:v((()=>[(m(),d(I(e.icon),{size:e.size},null,8,["size"])),_(t.$slots,"content",{},void 0,!0)])),_:3},8,["class","disabled"])):C("",!0)])])),default:v((()=>[E(" "+w(e.tooltip),1)])),_:3}))}),[["__scopeId","data-v-bf9b98c8"]]),ti=Oa(i({__name:"ColorPicker",props:{show:{type:Boolean,default:!0},type:{type:String,required:!0},editor:{type:Object,required:!0},colorType:{type:String,default:"color"},tooltip:{type:String,default:""}},setup(e){const t=e,a=o((()=>"color"===t.colorType?rn:sn)),l=o((()=>t.tooltip?t.tooltip:"color"===t.colorType?"文字颜色":"背景色")),i=o((()=>{if(!t.editor)return!1;try{if("color"===t.colorType){const e=t.editor.isActive("textStyle"),a=t.editor.getAttributes("textStyle");return e&&!!a.color}return t.editor.isActive("highlight")}catch(e){return!1}})),r=n({visible:!1,value:"#000000FF",type:""}),s=o({get:()=>r.value.value,set:e=>{r.value.value=e}}),u=o((()=>r.value.visible&&r.value.type===t.type)),h=()=>"",f=["#FFFFFF","#18A058","#2080F0","#F0A020","rgba(208, 48, 80, 1)"];let w=null;const y=()=>{var e,a;k();const l=null==(e=t.editor)?void 0:e.state.selection;l&&!l.empty&&(null==(a=t.editor)||a.chain().setTextSelection(l.to).run())},b=e=>{const t=e.target,a=document.querySelectorAll(".n-color-picker, .n-color-picker-panel, .n-popover");let l=!1;a.forEach((e=>{e.contains(t)&&(l=!0)})),w&&w.contains(t)||l||(r.value.visible=!1,y())},k=()=>{document.removeEventListener("click",b,!0),document.removeEventListener("mousedown",b,!0),w=null},x=e=>{var a,l;"color"===t.colorType?null==(a=t.editor)||a.chain().setColor(e).run():null==(l=t.editor)||l.chain().setHighlight({color:e}).run()};return z((()=>{k()})),(t,n)=>(m(),d(ei,{icon:a.value,show:e.show,trigger:t=>((e,t)=>{t.stopPropagation(),w=t.currentTarget;const a=r.value.visible&&r.value.type===e;r.value.visible=!a,r.value.type=e,r.value.visible?c((()=>{setTimeout((()=>{document.addEventListener("click",b,!0),document.addEventListener("mousedown",b,!0)}),0)})):y()})(e.type,t),"is-active":i.value,tooltip:l.value},{content:v((()=>[p(g(ie),{"show-preview":!1,size:"small",placement:"top",to:"body",value:s.value,"onUpdate:value":[n[0]||(n[0]=e=>s.value=e),x],"popover-style":"min-width: 220px; z-index: 10001; pointer-events: auto;","render-label":h,swatches:f,show:u.value},null,8,["value","show"])])),_:1},8,["icon","show","trigger","is-active","tooltip"]))}}),[["__scopeId","data-v-93352d2c"]]),ai=i({__name:"ToolbarButtonGroup",props:{buttons:{},editor:{},extensionsSet:{},showModal:{type:Function},modal:{}},emits:["image-upload","toggle-fullscreen"],setup(e,{emit:t}){const a=e,l=t,n=o((()=>a.buttons.filter((e=>a.extensionsSet.has(e.extensionName)))));return(e,t)=>(m(!0),f(L,null,S(n.value,(t=>{var n;return m(),d(ei,{key:t.tooltip,icon:t.icon,show:e.extensionsSet.has(t.extensionName),trigger:()=>(e=>{e.emit?"image-upload"===e.emit?l("image-upload"):"toggle-fullscreen"===e.emit&&l("toggle-fullscreen"):e.trigger(a.editor,a.showModal,a.modal)})(t),"is-active":(null==(n=t.isActive)?void 0:n.call(t,e.editor))||!1,tooltip:t.tooltip},null,8,["icon","show","trigger","is-active","tooltip"])})),128))}}),li={textFormat:[{icon:El,extensionName:"bold",trigger:e=>null==e?void 0:e.chain().focus().toggleBold().run(),isActive:e=>null==e?void 0:e.isActive("bold"),tooltip:"加粗"},{icon:Ln,extensionName:"italic",trigger:e=>null==e?void 0:e.chain().focus().toggleItalic().run(),isActive:e=>null==e?void 0:e.isActive("italic"),tooltip:"斜体"},{icon:Il,extensionName:"strike",trigger:e=>null==e?void 0:e.chain().focus().toggleStrike().run(),isActive:e=>null==e?void 0:e.isActive("strike"),tooltip:"删除线"},{icon:_l,extensionName:"underline",trigger:e=>null==e?void 0:e.chain().focus().toggleUnderline().run(),isActive:e=>null==e?void 0:e.isActive("underline"),tooltip:"下划线"},{icon:en,extensionName:"code",trigger:e=>null==e?void 0:e.chain().focus().toggleCode().run(),isActive:e=>null==e?void 0:e.isActive("code"),tooltip:"行内代码"}],heading:[{icon:Ml,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:1}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:1}),tooltip:"标题1"},{icon:Rl,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:2}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:2}),tooltip:"标题2"},{icon:Al,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:3}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:3}),tooltip:"标题3"}],list:[{icon:Fl,extensionName:"bulletList",trigger:e=>null==e?void 0:e.chain().focus().toggleBulletList().run(),isActive:e=>null==e?void 0:e.isActive("bulletList"),tooltip:"无序列表"},{icon:Ol,extensionName:"orderedList",trigger:e=>null==e?void 0:e.chain().focus().toggleOrderedList().run(),isActive:e=>null==e?void 0:e.isActive("orderedList"),tooltip:"有序列表"},{icon:Hl,extensionName:"taskList",trigger:e=>null==e?void 0:e.chain().focus().toggleTaskList().run(),isActive:e=>null==e?void 0:e.isActive("taskList"),tooltip:"任务列表"}],align:[{icon:jl,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("left").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"left"}),tooltip:"左对齐"},{icon:Nl,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("center").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"center"}),tooltip:"居中对齐"},{icon:ql,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("right").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"right"}),tooltip:"右对齐"},{icon:Yl,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("justify").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"justify"}),tooltip:"两端对齐"}],other:[{icon:un,extensionName:"blockquote",trigger:e=>null==e?void 0:e.chain().focus().toggleBlockquote().run(),isActive:e=>null==e?void 0:e.isActive("blockquote"),tooltip:"引用"},{icon:zl,extensionName:"codeBlockLowlight",trigger:e=>null==e?void 0:e.chain().focus().toggleCodeBlock().run(),isActive:e=>null==e?void 0:e.isActive("codeBlock"),tooltip:"代码块"},{icon:Dl,extensionName:"horizontalRule",trigger:e=>null==e?void 0:e.chain().focus().setHorizontalRule().run(),tooltip:"分割线"},{icon:Kl,extensionName:"link",trigger:(e,t)=>null==t?void 0:t("插入链接",(()=>{})),isActive:e=>null==e?void 0:e.isActive("link"),tooltip:"链接"},{icon:Pl,extensionName:"image",trigger:()=>{},tooltip:"图片",emit:"image-upload"},{icon:Cn,extensionName:"bilibili",trigger:(e,t,a)=>null==t?void 0:t("插入bilibili视频链接",(()=>null==e?void 0:e.commands.setBilibiliVideo({src:null==a?void 0:a.inputValue})),!0),tooltip:"B站视频"},{icon:Bl,extensionName:"history",trigger:e=>null==e?void 0:e.chain().focus().undo().run(),tooltip:"撤销"},{icon:$l,extensionName:"history",trigger:e=>null==e?void 0:e.chain().focus().redo().run(),tooltip:"重做"}]},ni=100,ii="parent",oi=["image","bilibili","codeBlock"],ri=e=>oi.includes(e),si=(e,t,a)=>{let l=!1;return e.doc.nodesBetween(t,a,(e=>{if("codeBlock"===e.type.name)return l=!0,!1})),l},ci=(e,t)=>!t.empty&&!!e.isEditable,ui=e=>{var t;const{editor:a,state:l}=e,{selection:n}=l;if(!ci(a,n))return!1;const i=n.node;if((null==(t=null==i?void 0:i.type)?void 0:t.name)&&ri(i.type.name))return!1;const{from:o,to:r}=n;return!si(l,o,r)};const di={class:"editor-bubble-menu"},mi=i({__name:"EditorBubbleMenu",props:{editor:{},extensionsSet:{},selectBubbleMenu:{}},emits:["show-modal"],setup(e,{emit:t}){const a={textFormat:li.textFormat,align:li.align,other:[li.other[0]]},l=t,{tippyOptions:n,shouldShow:i}={tippyOptions:{duration:ni,appendTo:ii},shouldShow:ui,isExcludedNodeType:ri,hasCodeBlockInSelection:si,checkBasicConditions:ci},o=(e,t,a=!1)=>{l("show-modal",{title:e,trigger:t,onlyInputValue:a})};return(e,t)=>(m(),d(g($t),{"tippy-options":g(n),editor:e.editor,"should-show":g(i)},{default:v((()=>{var t;return[h("div",di,[e.selectBubbleMenu.image?(m(),f(L,{key:0},[],64)):e.selectBubbleMenu.bilibili?(m(),f(L,{key:1},[],64)):(m(),f(L,{key:2},[p(ai,{buttons:a.textFormat,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":o},null,8,["buttons","editor","extensions-set"]),p(ai,{buttons:[a.other[0]],editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":o},null,8,["buttons","editor","extensions-set"]),p(ei,{icon:g(Kl),show:e.extensionsSet.has("link"),trigger:()=>o("设置链接",(()=>{var t;null==(t=e.editor)||t.chain().focus().extendMarkRange("link").run()}),!0),"is-active":null==(t=e.editor)?void 0:t.isActive("link"),tooltip:"链接"},null,8,["icon","show","trigger","is-active"]),p(ti,{show:e.extensionsSet.has("color"),type:"bubble-menu",editor:e.editor,colorType:"color",tooltip:"文字颜色"},null,8,["show","editor"]),p(ti,{show:e.extensionsSet.has("backgroundColor"),type:"bubble-menu",editor:e.editor,colorType:"backgroundColor",tooltip:"背景色"},null,8,["show","editor"]),p(ai,{buttons:a.align,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":o},null,8,["buttons","editor","extensions-set"])],64))])]})),_:1},8,["tippy-options","editor","should-show"]))}}),vi={class:"editor-floating-menu"},pi=i({__name:"EditorFloatingMenu",props:{editor:{type:Object,required:!0},extensionsSet:{type:Object,required:!0}},emits:["image-upload","show-modal"],setup(e,{emit:t}){const a=e,l=t,n={duration:100,appendTo:"parent",placement:"right"},i=()=>{var e;null==(e=a.editor)||e.chain().focus().setLink({href:""}).run()},o=e=>{const{editor:t,state:a}=e,{selection:l}=a,{$anchor:n}=l,i="paragraph"===n.parent.type.name,o=0===n.parent.content.size,r="codeBlock"===n.parent.type.name;let s=!1;const c=n.pos,u=a.doc;if(c>0){"codeBlock"===u.resolve(c-1).parent.type.name&&(s=!0)}if(c<u.content.size){"codeBlock"===u.resolve(c+1).parent.type.name&&(s=!0)}return i&&o&&t.isEditable&&!r&&!s};return(t,a)=>(m(),d(g(Dt),{"tippy-options":n,editor:e.editor,"should-show":o},{default:v((()=>{var a,n,o,r,s,c;return[h("div",vi,[p(ei,{icon:g(Al),show:e.extensionsSet.has("heading"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().toggleHeading({level:3}).run()},"is-active":null==(a=e.editor)?void 0:a.isActive("heading",{level:3}),tooltip:"标题3"},null,8,["icon","show","trigger","is-active"]),p(ei,{icon:g(Kl),show:e.extensionsSet.has("link"),trigger:()=>((e,t,a=!1)=>{l("show-modal",{title:e,trigger:t,onlyInputValue:a})})("插入链接",i),"is-active":null==(n=e.editor)?void 0:n.isActive("link"),tooltip:"链接"},null,8,["icon","show","trigger","is-active"]),p(ei,{icon:g(Pl),show:e.extensionsSet.has("image"),trigger:()=>t.$emit("image-upload"),tooltip:"图片"},null,8,["icon","show","trigger"]),p(ti,{show:e.extensionsSet.has("color"),type:"floating-menu",editor:e.editor,colorType:"color",tooltip:"文字颜色"},null,8,["show","editor"]),p(ti,{show:e.extensionsSet.has("backgroundColor"),type:"floating-menu",editor:e.editor,colorType:"backgroundColor",tooltip:"背景色"},null,8,["show","editor"]),p(ei,{icon:g(jl),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("left").run()},"is-active":null==(o=e.editor)?void 0:o.isActive({textAlign:"left"}),tooltip:"左对齐"},null,8,["icon","show","trigger","is-active"]),p(ei,{icon:g(Nl),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("center").run()},"is-active":null==(r=e.editor)?void 0:r.isActive({textAlign:"center"}),tooltip:"居中对齐"},null,8,["icon","show","trigger","is-active"]),p(ei,{icon:g(ql),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("right").run()},"is-active":null==(s=e.editor)?void 0:s.isActive({textAlign:"right"}),tooltip:"右对齐"},null,8,["icon","show","trigger","is-active"]),p(ei,{icon:g(Yl),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("justify").run()},"is-active":null==(c=e.editor)?void 0:c.isActive({textAlign:"justify"}),tooltip:"两端对齐"},null,8,["icon","show","trigger","is-active"])])]})),_:1},8,["editor"]))}}),gi=()=>{const{modal:e,showModal:t}=(()=>{const e=n({visible:!1,title:"",inputTitle:"",inputValue:"",onlyInputValue:!1,trigger:()=>{}}),t=()=>{e.value.visible=!1};return{modal:e,showModal:t=>{const{title:a,trigger:l,onlyInputValue:n=!1}=t;e.value.inputTitle="",e.value.inputValue="",e.value.visible=!0,e.value.title=a,e.value.trigger=l,e.value.onlyInputValue=n},closeModal:t,handleConfirm:a=>{a?a(e.value):e.value.trigger(),t()},handleCancel:()=>{t()}}})();return{modal:e,handleShowModal:t}},hi={class:"flex-column-gap12"},fi=Oa(i({__name:"EditorModalHandler",props:{modal:{},editor:{}},emits:["update:modal"],setup(e,{emit:t}){const a=e,l=t,n=()=>{if(a.editor)if("插入链接"===a.modal.title){const e=!a.editor.state.selection.empty;a.modal.inputValue&&(e?a.editor.chain().focus().setLink({href:a.modal.inputValue}).run():a.modal.inputTitle?a.editor.chain().focus().insertContent(`<a href="${a.modal.inputValue}">${a.modal.inputTitle}</a>`).run():a.editor.chain().focus().insertContent(`<a href="${a.modal.inputValue}">${a.modal.inputValue}</a>`).run())}else"设置链接"===a.modal.title?a.modal.inputValue&&a.editor.chain().focus().extendMarkRange("link").setLink({href:a.modal.inputValue}).run():"插入bilibili视频链接"===a.modal.title?a.modal.inputValue&&a.editor.commands.setBilibiliVideo({src:a.modal.inputValue}):a.modal.trigger();else a.modal.trigger();const e={...a.modal,visible:!1};l("update:modal",e)},i=()=>{const e={...a.modal,visible:!1};l("update:modal",e)};return(e,t)=>(m(),d(g(re),{show:e.modal.visible,"onUpdate:show":t[2]||(t[2]=t=>e.modal.visible=t),title:e.modal.title,preset:"dialog","positive-text":"确认","negative-text":"取消",onPositiveClick:n,onNegativeClick:i,"auto-focus":!1},{default:v((()=>[h("div",hi,[e.modal.onlyInputValue?C("",!0):(m(),d(g(oe),{key:0,value:e.modal.inputTitle,"onUpdate:value":t[0]||(t[0]=t=>e.modal.inputTitle=t),placeholder:"请输入标题"},null,8,["value"])),p(g(oe),{value:e.modal.inputValue,"onUpdate:value":t[1]||(t[1]=t=>e.modal.inputValue=t),placeholder:"请输入链接"},null,8,["value"])])])),_:1},8,["show","title"]))}}),[["__scopeId","data-v-7bcec345"]]),wi=Oa(i({__name:"LongPress",props:{duration:{type:Number,default:500}},emits:["long-press","click"],setup(e,{emit:t}){const a=e,l=t;let i=null,o=null,r=null;const s=n(!1),c=()=>{o=Date.now(),i=setTimeout((()=>{s.value=!0,l("long-press")}),a.duration)},u=()=>{if(null!==i){clearTimeout(i);Date.now()-(o??0)<a.duration&&(null!==r&&clearTimeout(r),r=setTimeout((()=>{l("click")}),200))}s.value=!1},d=()=>{null!==i&&clearTimeout(i),s.value=!1};return(e,t)=>(m(),f("div",{class:"long-press-wrapper",onMousedown:c,onMouseup:u,onMouseleave:d,onTouchstartPassive:c,onTouchendPassive:u,onTouchcancelPassive:d},[h("div",{class:y(["slot-content",{"long-press-active":s.value}])},[_(e.$slots,"default",{},void 0,!0)],2)],32))}}),[["__scopeId","data-v-be38eebc"]]),yi=i({__name:"FormatPainterBtn",props:{editor:{}},setup(e){const t=e,a=o((()=>{var e;const{selection:a}=(null==(e=t.editor)?void 0:e.state)??{};return!!a&&a.from!==a.to})),l=()=>{var e;const{selection:a}=null==(e=t.editor)?void 0:e.state,{from:l,to:n}=a;if(l!==n){const e=t.editor.state.tr;t.editor.state.doc.nodesBetween(l,n,((t,a)=>{t.isText&&e.removeMark(a,a+t.nodeSize,null)})),t.editor.view.dispatch(e),t.editor.commands.focus()}},n=()=>{var e,a;const{formatPainter:l}=null==(e=t.editor)?void 0:e.storage,{selection:n}=null==(a=t.editor)?void 0:a.state,{from:i,to:o}=n;if(l.isActive){if(l.sourceMarks&&i!==o){const e=t.editor.state.tr;t.editor.state.doc.nodesBetween(i,o,((t,a)=>{t.isText&&e.removeMark(a,a+t.nodeSize,null)})),l.sourceMarks.forEach((t=>{e.addMark(i,o,t)})),t.editor.view.dispatch(e),t.editor.commands.focus(),l.isActive=!1,l.sourceNode=null,l.sourceMarks=null}}else{const e=t.editor.state.doc.nodeAt(i);e&&(l.isActive=!0,l.sourceNode=e,l.sourceMarks=[...e.marks],t.editor.commands.focus())}};return r((()=>{t.editor&&t.editor.on("selectionUpdate",(()=>{var e,a;const{formatPainter:l}=(null==(e=t.editor)?void 0:e.storage)||{},{selection:n}=(null==(a=t.editor)?void 0:a.state)||{},{from:i,to:o}=n||{};if(i===o&&(null==l?void 0:l.isActive)){const e=t.editor.state.tr;e.setMeta("addToHistory",!1),t.editor.view.dispatch(e)}}))})),z((()=>{t.editor&&t.editor.off("selectionUpdate")})),(e,t)=>(m(),d(wi,{duration:500,onLongPress:l,onClick:n},{default:v((()=>{var l,n,i,o,r,s;return[p(ei,{"is-active":(null==(i=null==(n=null==(l=e.editor)?void 0:l.storage)?void 0:n.formatPainter)?void 0:i.isActive)??!1,icon:g(cn),tooltip:(null==(s=null==(r=null==(o=e.editor)?void 0:o.storage)?void 0:r.formatPainter)?void 0:s.isActive)?"点击应用格式":"格式刷",show:!0,trigger:()=>{},disabled:!a.value,onMousedown:t[0]||(t[0]=P((()=>{}),["prevent"])),onClick:t[1]||(t[1]=P((()=>{}),["prevent"]))},null,8,["is-active","icon","tooltip","disabled"])]})),_:1}))}}),bi=i({__name:"EditorToolbar",props:{editor:{},extensionsSet:{},toolbarClass:{default:"editor-toolbar"},externalFullscreenState:{type:Boolean,default:void 0},modal:{default:()=>({inputValue:"",inputTitle:""})}},emits:["image-upload","show-modal","toggle-fullscreen"],setup(e,{emit:t}){const a=e,l=t,i=n(a.modal),o=n(!1);s((()=>a.externalFullscreenState),(e=>{void 0!==e&&(o.value=e)}));const r=(e,t,a=!1)=>{l("show-modal",{title:e,trigger:t,onlyInputValue:a})},c=()=>{o.value=!o.value,l("toggle-fullscreen",o.value)};return(e,t)=>(m(),f("div",{class:y(e.toolbarClass)},[p(ai,{buttons:g(li).textFormat,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:i.value,onImageUpload:t[0]||(t[0]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),p(ai,{buttons:g(li).heading,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:i.value,onImageUpload:t[1]||(t[1]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),p(ai,{buttons:g(li).list,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:i.value,onImageUpload:t[2]||(t[2]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),p(ai,{buttons:[g(li).other[0],g(li).other[1]],editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:i.value,onImageUpload:t[3]||(t[3]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),p(ti,{show:e.extensionsSet.has("color"),type:"toolbar",editor:e.editor,colorType:"color",tooltip:"文字颜色"},null,8,["show","editor"]),p(ti,{show:e.extensionsSet.has("backgroundColor"),type:"toolbar",editor:e.editor,colorType:"backgroundColor",tooltip:"背景色"},null,8,["show","editor"]),p(ai,{buttons:g(li).other.slice(2),editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:i.value,onImageUpload:t[4]||(t[4]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),p(yi,{show:e.extensionsSet.has("formatPainter"),editor:e.editor},null,8,["show","editor"]),p(ai,{buttons:g(li).align,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:i.value,onImageUpload:t[5]||(t[5]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),p(ei,{icon:o.value?g(Vl):g(Ul),show:e.extensionsSet.has("fullscreen"),trigger:c,"is-active":o.value,tooltip:o.value?"退出全屏":"全屏"},null,8,["icon","show","is-active","tooltip"])],2))}}),ki=["image","dropcursor","placeholder","link","textStyle","color","backgroundColor","mention","bilibili","align","history"],xi=1e4,Ci={key:0,class:"character-count"},Li={key:6,class:"character-count-fullscreen"},Si=["accept"],Ti=Oa(i({__name:"TipTapEditor",props:{modelValue:{type:[Object,String],default:""},extensions:{type:Array,default:()=>[]},allExtensions:{type:Boolean,default:!1},toolbar:{type:Boolean,default:!1},placeholder:{type:String,default:"..."},editable:{type:Boolean,default:!0},fileBucket:{type:String,required:!0},bubbleMenu:{type:Boolean,default:!1},floatingMenu:{type:Boolean,default:!1},editorProps:{type:Object,default:()=>({attributes:{class:"ProseMirror"}})},toolbarClass:{type:Object,default:["editor-toolbar"]},showCharacterCount:{type:Boolean,default:!1},characterLimit:{type:Number,default:1e3},useThumbnail:{type:Boolean,default:!0}},emits:["update:modelValue","save"],setup(e,{expose:t,emit:a}){const l=e,i=a,{imageInputRef:o,handleImageChange:s,imageHandleCallback:c}=yl(),{modal:u,handleShowModal:b}=gi(),{editor:k,initEditor:x,watchEditable:L,clearContent:S,setContent:T,getMarkdown:M,cleanupEditor:R}=Gn({...l,modelValue:"string"==typeof l.modelValue?{}:l.modelValue},i,(()=>{var e;return null==(e=o.value)?void 0:e.click()}),((e,t,a)=>{b({title:e,trigger:t,onlyInputValue:a})})),{isFullscreen:A,toolbarFullscreenState:E,handleToggleFullscreen:_,handleCloseFullscreen:I,cleanupFullscreen:P}=(e=>{const t=n(!1),a=n(void 0);let l=null,i=null,o=null;const r=n=>{if(t.value=n,a.value=n,t.value){if(document.body.style.overflow="hidden",window.innerWidth<=768){if(o&&window.removeEventListener("resize",o),i){const e=window.visualViewport;e&&e.removeEventListener("resize",i)}l=()=>{const e=document.querySelector(".tiptap-fullscreen"),a=null==e?void 0:e.querySelector(".editor-content");if(e&&a&&t.value){const e=window.innerHeight;a.style.maxHeight=e-128+"px"}},o=e=>{l&&l()};const e=window.visualViewport;if(e&&(i=a=>{const l=document.querySelector(".tiptap-fullscreen");if(l&&t.value){const t=e.offsetTop;l.style.transform=t?`translateY(-${t}px)`:"";const a=l.querySelector(".editor-content");if(a){const t=e.height-128;a.style.maxHeight=`${t}px`}}},e.addEventListener("resize",i)),l)try{l()}catch(r){ta.error("Error initializing resize handler",r)}o&&window.addEventListener("resize",o)}const e=document.documentElement.classList.contains("dark-theme");setTimeout((()=>{const t=document.querySelector(".tiptap-fullscreen");if(t){e&&t.classList.add("dark-theme");const a=document.querySelector(".tiptap-editor-wrapper:not(.tiptap-fullscreen)")||document.querySelector(".article-modal-content");if(a){const l=window.getComputedStyle(a),n="rgba(0, 0, 0, 0)"!==l.backgroundColor&&"transparent"!==l.backgroundColor?l.backgroundColor:e?"var(--dark-gray)":"white";t.style.backgroundColor=n;const i=t.querySelector(".editor-content"),o=t.querySelector(".editor-content-fullscreen");i&&(i.style.backgroundColor=n),o&&(o.style.backgroundColor=n);const r=t.querySelector(".editor-toolbar");r&&(r.style.backgroundColor=n);const s=t.querySelector(".ProseMirror");s&&(s.style.backgroundColor=n)}else{const a=e?"var(--dark-gray)":"white";t.style.backgroundColor=a;const l=t.querySelector(".editor-content"),n=t.querySelector(".editor-content-fullscreen");l&&(l.style.backgroundColor=a),n&&(n.style.backgroundColor=a);const i=t.querySelector(".editor-toolbar");i&&(i.style.backgroundColor=a);const o=t.querySelector(".ProseMirror");o&&(o.style.backgroundColor=a)}}}),0)}else{document.body.style.overflow="",o&&(window.removeEventListener("resize",o),o=null);const e=window.visualViewport;i&&e&&(e.removeEventListener("resize",i),i=null),l=null;const t=document.querySelector(".tiptap-fullscreen");t&&(t.style.transform="")}requestAnimationFrame((()=>{var t;null==(t=e.value)||t.commands.focus()}))};return{isFullscreen:t,toolbarFullscreenState:a,handleToggleFullscreen:r,handleCloseFullscreen:()=>{r(!1),a.value=!1},cleanupFullscreen:()=>{document.body.style.overflow="",o&&(window.removeEventListener("resize",o),o=null);const e=window.visualViewport;i&&e&&(e.removeEventListener("resize",i),i=null),l=null}}})(k);let $,D={image:!1,bilibili:!1};r((()=>{$=x();const e=Zn(k,c,l.fileBucket,l.useThumbnail);e.selectBubbleMenu&&(D=e.selectBubbleMenu),e.setupEditorEvents(),L()}));return t({setContent:T,clearContent:S,getMarkdown:M,editor:k,handleSave:()=>{i("save")}}),z((()=>{R(),P()})),(t,a)=>g(k)?(m(),f("div",{key:0,class:y(["tiptap-editor-wrapper",{"tiptap-fullscreen":g(A)}]),style:{width:"100%"}},[g(A)?(m(),f("div",{key:0,class:"fullscreen-close-button",onClick:a[0]||(a[0]=(...e)=>g(I)&&g(I)(...e))},[p(g(ln),{size:24})])):C("",!0),l.toolbar?(m(),d(bi,{key:1,editor:g(k),"extensions-set":g($),"toolbar-class":e.toolbarClass,"external-fullscreen-state":g(E),modal:g(u),onImageUpload:a[1]||(a[1]=e=>{var t;return null==(t=g(o))?void 0:t.click()}),onShowModal:g(b),onToggleFullscreen:g(_)},null,8,["editor","extensions-set","toolbar-class","external-fullscreen-state","modal","onShowModal","onToggleFullscreen"])):C("",!0),g(k)&&l.bubbleMenu?(m(),d(mi,{key:2,editor:g(k),"extensions-set":g($),"select-bubble-menu":g(D),onShowModal:g(b)},null,8,["editor","extensions-set","select-bubble-menu","onShowModal"])):C("",!0),g(k)&&l.floatingMenu?(m(),d(pi,{key:3,editor:g(k),"extensions-set":g($),onShowModal:g(b),onImageUpload:a[2]||(a[2]=e=>{var t;return null==(t=g(o))?void 0:t.click()})},null,8,["editor","extensions-set","onShowModal"])):C("",!0),g(A)?(m(),d(g(ae),{key:4,class:y(["editor-content-fullscreen",{"editor-readonly":!l.editable}])},{default:v((()=>[p(g(Ut),{editor:g(k)},null,8,["editor"])])),_:1},8,["class"])):(m(),f("div",{key:5,class:y(["editor-content",{"editor-readonly":!l.editable}]),style:{width:"100%"}},[p(g(Ut),{editor:g(k)},null,8,["editor"]),l.showCharacterCount&&g(k)?(m(),f("div",Ci,w(g(k).storage.characterCount.characters)+" / "+w(l.characterLimit),1)):C("",!0)],2)),g(A)&&g(k)?(m(),f("div",Li,w(g(k).storage.characterCount.characters)+" / "+w(l.characterLimit),1)):C("",!0),p(fi,{modal:g(u),"onUpdate:modal":a[3]||(a[3]=e=>B(u)?u.value=e:null),editor:g(k)},null,8,["modal","editor"]),h("input",{type:"file",accept:g(_a).imageTypes.join(","),ref_key:"imageInputRef",ref:o,onChange:a[4]||(a[4]=e=>g(k)&&g(s)(e,g(k),l.fileBucket,l.useThumbnail)),class:"display-none"},null,40,Si)],2)):C("",!0)}}),[["__scopeId","data-v-5e0ad95f"]]),Mi=i({__name:"SearchUserSelect",props:{modelValue:{},placeholder:{}},emits:["update:modelValue"],setup(e,{expose:t,emit:a}){const l=e,i=a,r=n(!1),s=n([]),c=o((()=>{const e=[...s.value];return l.modelValue.forEach((t=>{e.some((e=>e.id===t.id))||e.push(t)})),e})),u=e=>{const t=_a.getResourceURL(e.avatar)||"";return ta.debug("渲染用户标签: ",e),R("div",{style:{display:"flex",alignItems:"center"}},[R(se,{size:"small",round:!0,objectFit:"cover",src:t,fallbackSrc:"/avatar/avatar.png",style:{marginRight:"8px",verticalAlign:"middle"}}),R("span",e.username)])},v=o({get:()=>l.modelValue.map((e=>e.id)),set:e=>{const t=l.modelValue.filter((t=>e.includes(t.id))),a=c.value.filter((a=>e.includes(a.id)&&!t.some((e=>e.id===a.id))));i("update:modelValue",[...t,...a])}}),p=e=>{e.trim()?(r.value=!0,bl.searchUser(e).then((e=>{e.data&&(s.value=e.data)})).finally((()=>{r.value=!1}))):s.value=[]};return t({reset:()=>{s.value=[]}}),(e,t)=>(m(),d(g(te),{value:v.value,"onUpdate:value":t[0]||(t[0]=e=>v.value=e),filterable:"",clearable:"",multiple:"",remote:"","value-field":"id","label-field":"username",options:c.value,loading:r.value,placeholder:e.placeholder,"render-label":u,onSearch:p},null,8,["value","options","loading","placeholder"]))}});var Ri=(e=>(e[e.PUBLIC=0]="PUBLIC",e[e.PERSONAL=1]="PERSONAL",e))(Ri||{});const Ai={0:"公开",1:"个人"};function Ei(){const e=n(null);return{articleFileInputRef:e,handleArticleFileClick:()=>{var t;null==(t=e.value)||t.click()},handleArticleFileChange:(t,a,l)=>{var n;const i=null==(n=t.target.files)?void 0:n[0];i&&(((e,t,a)=>{const l=new FileReader;l.readAsText(e,"UTF-8"),l.onload=e=>{var l,n;const i=(null==(l=e.target)?void 0:l.result).split("\n");ta.debug("import lines: ",i);const o=(i[0]?i[0].substring(1).trim():"")||"",r=i[1]?i[1].replace(">","").split(",").filter(Boolean).map((e=>e.trim())):[],s=null==(n=i[9])?void 0:n.split("|"),c=parseInt(s[0].replace(">","").trim())||0,u=parseInt(s[1].trim())||Ri.PERSONAL,d=i.slice(12).join("\n"),m=a.value.editor;if(m)try{if(m.storage.markdown){m.commands.clearContent(!1);const e=m.storage.markdown.parser.parse(d);m.commands.setContent(e||d,!0)}else m.commands.setContent(d,!0),xa.warning("Markdown 格式可能无法完全解析");t.value={...t.value,title:o,tags:r,operationLevel:c,publishedScope:u,contentObj:m.getJSON()||{}}}catch(v){ta.error("Error parsing or setting markdown content:",v),xa.error("解析 Markdown 内容时出错"),m.commands.setContent(d,!0),t.value={...t.value,title:o,tags:r,operationLevel:c,publishedScope:u,contentObj:m.getJSON()||{}}}else xa.error("编辑器尚未准备好")},l.onerror=()=>{xa.warning("文件貌似有问题~")}})(i,a,l),e.value&&(e.value.value=""))}}}function _i(e,t,a="内容不能为空哦~"){var l;if(!e){const e="编辑器初始化失败，请刷新后重试";return xa.warning(e),{isValid:!1,message:e}}const n="editor"in e?e.editor:e,i=n.isEmpty,o=!t.value;if(i)return xa.warning(a),{isValid:!1,message:a};if(o&&!i){const e=null==(l=n.getJSON)?void 0:l.call(n);return e&&"object"==typeof e?(t.value=e,{isValid:!0,content:e}):(xa.warning(a),{isValid:!1,message:a})}return{isValid:!0,content:t.value}}function Ii(e,t,a="正在处理中，请稍候..."){let l;if(e instanceof Map&&t)l=e.get(t)||!1;else{if(!("value"in e))return!0;l=e.value}return!l||(xa.warning(a),!1)}function zi(e,t,a){e instanceof Map&&a?e.set(a,t):"value"in e&&(e.value=t)}const Pi={autoFocus:!1,closeOnEsc:!0,maskClosable:!1};function Bi(e){return{...Pi,...e}}const{dialog:$i}=ee(["dialog"]),Di=e=>$i.warning(Bi(e));function Ui(){const e=n(!1),t=n(!1),a=n(!1),l=n(!1);return{isArticleDialogVisible:e,isEditingArticle:t,submitLoading:a,quickSaveLoading:l,openCreateArticleDialog:a=>{t.value=!1,a(),e.value=!0},openEditArticleDialog:(a,l)=>{t.value=!0,l(a),e.value=!0},handleClose:t=>(Di(Bi({title:"提示",content:"你确定关闭？",positiveText:"确定",negativeText:"不确定",onPositiveClick:()=>{t(),e.value=!1},onNegativeClick:()=>{}})),!1),setupKeyboardListener:t=>{const a=(t=>a=>{e.value&&a.ctrlKey&&"s"===a.key&&(a.preventDefault(),a.stopPropagation(),t())})(t);r((()=>{window.addEventListener("keydown",a)})),z((()=>{window.removeEventListener("keydown",a)}))}}}const Vi="article",Fi={class:"flex-between-center",style:{width:"min(16rem, 100%)"}},Oi={class:"article-editor-wrapper"},Hi={class:"article-editor-container"},ji={key:0,class:"character-count-external"},Ni=i({__name:"ArticleModal",emits:["success"],setup(e,{expose:t,emit:a}){const l=a,i=Ui(),r=function(){const e=n(i()),t=n(null),a=n(null),l=n(null);function i(){return{id:"",title:"",tags:[],operationLevel:0,publishedScope:Ri.PERSONAL,shareUsers:[],contentObj:{}}}const r=o((()=>{var e;const t=[],a=(null==(e=ia.getLoginUser())?void 0:e.level)??0;for(let l=0;l<=a;l++)t.push({label:"Lv"+l,value:l});return t})),s=(l,n,i,o,r)=>{var s;null==(s=t.value)||s.validate().then((()=>{var t;if(ta.debug("表单验证通过"),!Ii(i))return;const s=null==(t=a.value)?void 0:t.editor;if(!_i(s,{value:e.value.contentObj},"文章内容不能为空哦~").isValid)return;zi(i,!0);const u=null==s?void 0:s.getJSON(),d={title:e.value.title,tag:e.value.tags.join(","),operationLevel:e.value.operationLevel,publishedScope:e.value.publishedScope,content:u?qn.toJsonString(u):"",shareUserIds:e.value.shareUsers.map((e=>e.id))};n.value&&e.value.id&&Object.assign(d,{id:e.value.id}),(n.value&&e.value.id?Yn.edit:Yn.save)(d).then((t=>{(null==t?void 0:t.success)&&(!n.value&&t.data&&(n.value=!0,e.value.id=t.data.id,c((()=>{ta.debug("Article state updated:",{isEditing:n.value,articleId:e.value.id})}))),l?(o.value=!1,xa.success(n.value?"修改成功":"创建成功")):xa.success("保存成功"),r("success"))})).catch((t=>{n.value||(n.value=!1,e.value.id=""),xa.error(t.message||"保存失败")})).finally((()=>{i.value=!1}))})).catch((e=>{ta.debug("表单验证失败:",e)}))};return{articleForm:e,articleFormRef:t,articleTiptapEditorRef:a,shareUserSelectRef:l,generateCommentLevel:r,resetArticleForm:()=>{var t,n;e.value=i(),null==(t=l.value)||t.reset();const o=null==(n=a.value)?void 0:n.editor;o&&o.commands.clearContent(!0)},setFormData:t=>{e.value={id:t.id,title:t.title,tags:t.tags,operationLevel:t.operationLevel,publishedScope:t.publishedScope,contentObj:t.contentObj,shareUsers:t.shareUsers||[]},ta.debug("edit article form: ",e.value)},submitArticleForm:(e,t,l,n)=>{var i;if(t.value)return;const o=null==(i=a.value)?void 0:i.editor;ba("article-submit",(()=>{var i;!(null==(i=a.value)?void 0:i.editor)&&o&&(a.value={editor:o,setContent:()=>{},clearContent:()=>{},getMarkdown:()=>{},handleSave:()=>{}}),s(!0,e,t,l,n)}),300)},quickSaveArticleForm:(e,t,a,l)=>{t.value||ba("article-quick-save",(()=>{s(!1,e,t,a,l)}),300)}}}(),s=Ei(),{articleFormRef:u,articleTiptapEditorRef:y,shareUserSelectRef:b}=r,k={title:[{required:!0,message:"请输入文章标题",trigger:"blur"},{min:1,max:100,message:"标题长度在 1 到 100 个字符之间",trigger:"blur"}]},x=e=>{var t,a;const l={value:{editor:{isEmpty:(null==(t=r.articleTiptapEditorRef.value)?void 0:t.editor.isEmpty)||!1,getJSON:()=>{var e;return(null==(e=r.articleTiptapEditorRef.value)?void 0:e.editor.getJSON())||{}},commands:{clearContent:e=>{var t;return null==(t=r.articleTiptapEditorRef.value)?void 0:t.clearContent()},setContent:(e,t)=>{var a;return null==(a=r.articleTiptapEditorRef.value)?void 0:a.setContent(e)}},storage:(null==(a=r.articleTiptapEditorRef.value)?void 0:a.editor.storage)||{}}}};s.handleArticleFileChange(e,r.articleForm,l)};i.setupKeyboardListener((()=>M()));const T=()=>(r.submitArticleForm(i.isEditingArticle,i.submitLoading,i.isArticleDialogVisible,l),!1),M=()=>{r.quickSaveArticleForm(i.isEditingArticle,i.quickSaveLoading,i.isArticleDialogVisible,l)};return t({openCreateArticleDialog:()=>i.openCreateArticleDialog(r.resetArticleForm),openEditArticleDialog:e=>i.openEditArticleDialog(e,r.setFormData)}),(e,t)=>(m(),d(g(re),{show:g(i).isArticleDialogVisible.value,"onUpdate:show":t[6]||(t[6]=e=>g(i).isArticleDialogVisible.value=e),preset:"dialog","negative-text":"算了","positive-text":"确认",onNegativeClick:t[7]||(t[7]=()=>g(i).handleClose(g(r).resetArticleForm)),onPositiveClick:T,showIcon:!1,onClose:t[8]||(t[8]=()=>g(i).handleClose(g(r).resetArticleForm)),onMaskClick:t[9]||(t[9]=()=>g(i).handleClose(g(r).resetArticleForm)),"mask-closable":!1,"auto-focus":!1,"close-on-esc":!1,onEsc:t[10]||(t[10]=()=>g(i).handleClose(g(r).resetArticleForm)),class:"article-modal","positive-button-props":{loading:g(i).submitLoading.value}},{header:v((()=>[p(g(ge),{type:"primary",size:20},{default:v((()=>[E(w(g(i).isEditingArticle.value?"是得再改改":"想点什么呢"),1)])),_:1}),p(g(yn),{size:24,class:"cursor-pointer",onClick:g(s).handleArticleFileClick},null,8,["onClick"]),h("input",{type:"file",ref:"articleFile.articleFileInputRef",accept:".md",onChange:x,class:"display-none"},null,544)])),default:v((()=>[p(g(ce),{model:g(r).articleForm.value,rules:k,ref_key:"articleFormRef",ref:u,"label-placement":"left"},{default:v((()=>[p(g(ue),{label:"标题",path:"title",style:{width:"min(30rem, 100%)"}},{default:v((()=>[p(g(oe),{value:g(r).articleForm.value.title,"onUpdate:value":t[0]||(t[0]=e=>g(r).articleForm.value.title=e),placeholder:"请输入文章标题"},null,8,["value"])])),_:1}),p(g(ue),{label:"标签",path:"tag",style:{width:"min(30rem, 100%)"}},{default:v((()=>[p(g(de),{value:g(r).articleForm.value.tags,"onUpdate:value":t[1]||(t[1]=e=>g(r).articleForm.value.tags=e),"input-props":{maxlength:20},max:3,type:"primary",placeholder:"请输入标签"},null,8,["value"])])),_:1}),h("div",Fi,[p(g(ue),{label:"等级 | 范围",path:"allowCommentLevel",style:{width:"6rem"}},{default:v((()=>[p(g(me),{value:g(r).articleForm.value.operationLevel,"onUpdate:value":t[2]||(t[2]=e=>g(r).articleForm.value.operationLevel=e),options:g(r).generateCommentLevel.value,size:"small",trigger:"click"},{default:v((()=>[p(g(ne),{size:"small"},{default:v((()=>[E(" Lv"+w(g(r).articleForm.value.operationLevel||"0"),1)])),_:1})])),_:1},8,["value","options"])])),_:1}),p(g(ue),{path:"scope"},{default:v((()=>[p(g(ve),{value:g(r).articleForm.value.publishedScope,"onUpdate:value":t[3]||(t[3]=e=>g(r).articleForm.value.publishedScope=e),size:"small","default-value":g(Ri).PERSONAL},{default:v((()=>[(m(!0),f(L,null,S([{value:g(Ri).PUBLIC,label:g(Ai)[g(Ri).PUBLIC]},{value:g(Ri).PERSONAL,label:g(Ai)[g(Ri).PERSONAL]}],(e=>(m(),d(g(pe),{class:"flex-between-center",key:e.value,value:e.value,label:e.label},null,8,["value","label"])))),128))])),_:1},8,["value","default-value"])])),_:1})]),g(r).articleForm.value.publishedScope===g(Ri).PERSONAL?(m(),d(g(ue),{key:0,label:"分享给",path:"shareUsers",style:{width:"min(30rem, 100%)"}},{default:v((()=>[p(Mi,{modelValue:g(r).articleForm.value.shareUsers,"onUpdate:modelValue":t[4]||(t[4]=e=>g(r).articleForm.value.shareUsers=e),placeholder:"请搜索并选择用户",ref_key:"shareUserSelectRef",ref:b},null,8,["modelValue"])])),_:1})):C("",!0),p(g(ue),{path:"content"},{default:v((()=>{var e;return[h("div",Oi,[h("div",Hi,[p(g(ae),{class:"article-modal-content"},{default:v((()=>[p(Ti,{ref_key:"articleTiptapEditorRef",ref:y,modelValue:g(r).articleForm.value.contentObj,"onUpdate:modelValue":t[5]||(t[5]=e=>g(r).articleForm.value.contentObj=e),"editor-props":{attributes:{class:"ProseMirrorNoneOutline"}},"bubble-menu":!0,"floating-menu":!0,"file-bucket":g(Vi),"all-extensions":!0,toolbar:!0,"toolbar-class":["editor-toolbar","editor-toolbar-bgc"],placeholder:"尽情发挥！","show-character-count":!1,"character-limit":g(xi),"save-loading":g(i).quickSaveLoading.value,onSave:M},null,8,["modelValue","file-bucket","character-limit","save-loading"])])),_:1})]),(null==(e=g(y))?void 0:e.editor)?(m(),f("div",ji,w(g(y).editor.storage.characterCount.characters)+" / "+w(g(xi)),1)):C("",!0)])]})),_:1})])),_:1},8,["model"])])),_:1},8,["show","positive-button-props"]))}}),qi={class:"article-info-container"},Yi={class:"article-header"},Wi={class:"article-header-content-wrapper",style:{"max-width":"80%"}},Ji={class:"article-header-content"},Ki={class:"article-tag-container"},Xi={class:"flex-column-center",style:{width:"80%",gap:"0.25rem"}},Gi={class:"action-buttons-container"},Zi={class:"interaction-container"},Qi={class:"comment-count-container",style:{"margin-right":"0"}},eo={class:"article-content flex-column-gap24"},to=i({__name:"ArticleSkeleton",props:{show:{type:Boolean,default:!1}},setup:e=>(t,a)=>$((m(),f("div",qi,[h("div",Yi,[h("div",Wi,[h("div",Ji,[p(g(he),{width:600,text:"",size:"large",style:{"max-width":"100%",margin:"1.25rem 0"}}),h("div",Ki,[p(g(he),{style:{width:"60%","margin-bottom":"0.5rem"},round:"",text:"",size:"small"})])])]),h("div",Xi,[p(g(he),{style:{width:"30%","margin-bottom":"0.25rem"},text:"",height:10,size:"small",repeat:3})]),h("div",Gi,[h("div",Zi,[p(g(he),{width:60,round:"",style:{"max-width":"100%"},text:"",size:"small"})]),h("div",Qi,[p(g(he),{height:20,width:100,round:"",style:{"max-width":"100%"},text:"",size:"small"})])])]),h("div",eo,[p(g(he),{style:{width:"100%"},round:"",text:"",size:"large",repeat:8})])],512)),[[D,e.show]])}),ao={URL:"/core/notifications",load:async e=>(await La(ao.URL,e)).data,read:async e=>(await Aa(ao.URL+"/"+e+"/read-status")).data,readAll:async()=>(await Aa(ao.URL+"/read-status")).data,unreadCount:async()=>(await La(ao.URL+"/total-unread")).data};var lo=(e=>(e[e.CLOSE=0]="CLOSE",e[e.ALL=1]="ALL",e[e.PUBLISH=2]="PUBLISH",e[e.MODIFY=3]="MODIFY",e[e.FAVORITE=4]="FAVORITE",e[e.SHARE=5]="SHARE",e))(lo||{});const no={0:"关闭",1:"全部",2:"发布",3:"修改",4:"收藏",5:"分享"},io=Oa(i({__name:"NotificationButton",props:{unreadCount:{type:Number,default:0},notificationReceiveType:{type:Number,required:!0}},emits:["click","long-press"],setup(e,{emit:t}){const a=n(null);return(t,l)=>(m(),f("div",{class:"notification-btn",style:{cursor:"pointer"},ref_key:"buttonRef",ref:a},[p(g(fe),{max:99,value:e.unreadCount,"show-zero":!1,show:e.notificationReceiveType!==g(lo).CLOSE},{default:v((()=>[e.notificationReceiveType!==g(lo).CLOSE?(m(),d(g(tn),{key:0,size:24})):(m(),d(g(an),{key:1,size:24}))])),_:1},8,["value","show"])],512))}}),[["__scopeId","data-v-ccf792c3"]]),oo="comment";class ro{static toTimeString(e,t="YYYY-MM-DD HH:mm:ss"){const a=new Date(parseInt(e)),l={YYYY:a.getFullYear().toString(),MM:(a.getMonth()+1).toString().padStart(2,"0"),DD:a.getDate().toString().padStart(2,"0"),HH:a.getHours().toString().padStart(2,"0"),mm:a.getMinutes().toString().padStart(2,"0"),ss:a.getSeconds().toString().padStart(2,"0")};return t.replace(/YYYY|MM|DD|HH|mm|ss/g,(e=>l[e]))}static getCurrentTimestamp(){return Date.now()}static dateToTimestamp(e,t="YYYY-MM-DD HH:mm:ss"){const a={"YYYY-MM-DD HH:mm:ss":/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/g,"YYYY-MM-DD":/^(\d{4})-(\d{2})-(\d{2})$/g}[t];if(!a)return ta.warn(`不支持的日期格式: ${t}`),null;const l=e.match(a);if(!l)return ta.warn(`日期字符串 ${e} 不符合格式 ${t}`),null;const n=parseInt(l[1],10),i=parseInt(l[2],10)-1,o=parseInt(l[3],10);let r=0,s=0,c=0;"YYYY-MM-DD HH:mm:ss"===t&&(r=parseInt(l[4],10),s=parseInt(l[5],10),c=parseInt(l[6],10));return new Date(n,i,o,r,s,c).getTime()}static formatDate(e,t="YYYY-MM-DD HH:mm:ss"){const a={YYYY:e.getFullYear().toString(),MM:(e.getMonth()+1).toString().padStart(2,"0"),DD:e.getDate().toString().padStart(2,"0"),HH:e.getHours().toString().padStart(2,"0"),mm:e.getMinutes().toString().padStart(2,"0"),ss:e.getSeconds().toString().padStart(2,"0")};return t.replace(/YYYY|MM|DD|HH|mm|ss/g,(e=>a[e]))}static getRelativeTime(e){const t=Date.now()-parseInt(e);if(t<1e3)return"刚刚";if(t<6e4)return Math.floor(t/1e3)+"秒前";if(t<36e5)return Math.floor(t/6e4)+"分钟前";if(t<864e5)return Math.floor(t/36e5)+"小时前";if(t<2592e6)return Math.floor(t/864e5)+"天前";if(t<31104e6){const e=Math.floor(t/2592e6);return 6===e?"半年前":e+"个月前"}return Math.floor(t/31104e6)+"年前"}}const so={class:"notification-list-container"},co="评论了：",uo=Oa(i({__name:"NotificationList",props:{visible:{type:Boolean}},emits:["notification-click"],setup(e,{expose:t,emit:a}){const l=e,i=a,o=n(!1),c=n([]),u=[{label:"",value:5},{label:"",value:10},{label:"",value:15}];u.forEach((e=>{e.label=`${e.value}/页`}));const d=n({page:1,pageSize:5,showSizePicker:!0,showQuickJumper:!1,pageSlot:5,pageSizes:u,size:"medium",showQuickJumpDropdown:!1,prefix:e=>R("span",`第 ${e.page} 页 `),suffix:e=>R("span",`共 ${e.itemCount} 条`),onUpdatePage:e=>{d.value.page=e,y()},onUpdatePageSize:e=>{d.value.pageSize=e,d.value.page=1,y()}}),v=[{title:"通知时间",key:"ctTm",width:162},{title:"通知内容",key:"content",ellipsis:!0,render(e){const t=e.commentId,a=e.content,l=a.indexOf(co);return R("span",[R(fe,{style:"position: absolute;",dot:0===e.isRead,offset:[-4,0]}),R(be,{trigger:"click",placement:"top-start",style:"max-width:min(555px,84vw); margin-left:min(-180px,40vw)",flip:!1},{trigger:()=>R("span",{class:"cursor-pointer notification-content",onClick:e=>e.stopPropagation()},t?a.substring(0,l+4)+qn.serializeContent(b(a.substring(l+4))):e.content),default:()=>R("div",{class:"notification-popover-content"},[t?R("div",{style:"margin: 10px"},[a.substring(0,l+4),R(Ti,{fileBucket:oo,modelValue:b(a.substring(l+4)),extensions:[...ki],editable:!1})]):R("div",e.content),R(ne,{style:"margin-left:auto",class:"flex-column-end",text:!0,type:"primary",onClick:()=>{w(e)}},["让我看看",R(Jl,{size:16})])])})])}}],h=e=>({}),w=e=>{i("notification-click",e)},y=()=>{o.value=!0,ao.load({pageNum:d.value.page,pageSize:d.value.pageSize}).then((e=>{var t;const a=null==e?void 0:e.data;if(a){const e=a;null==(t=e.rows)||t.forEach((e=>{e.ctTm=ro.toTimeString(e.ctTm)})),d.value.itemCount=e.totalRows||0,d.value.pageCount=e.totalPage||0,c.value=e.rows||[]}})).finally((()=>{o.value=!1}))};s((()=>l.visible),(e=>{e&&y()}),{immediate:!0}),r((()=>{l.visible&&y()})),t({loadNotificationPage:y,resetPagination:()=>{d.value.page=1,d.value.pageSize=5}});const b=e=>{if(!e)return{type:"doc",content:[{type:"paragraph",content:[]}]};try{return qn.toJsonObject(e)}catch(t){return ta.error("通知内容JSON解析失败:",t),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"内容无法显示"}]}]}}};return(e,t)=>(m(),f("div",so,[p(g(we),{class:"notification-table",remote:!0,loading:o.value,data:c.value,"row-props":h,columns:v,bordered:!1},null,8,["loading","data"]),p(g(ye),{class:"notification-pagination",page:d.value.page,"page-size":d.value.pageSize,"show-size-picker":d.value.showSizePicker,"show-quick-jumper":d.value.showQuickJumper,"page-slot":d.value.pageSlot,"page-sizes":d.value.pageSizes,size:d.value.size,"show-quick-jump-dropdown":d.value.showQuickJumpDropdown,prefix:d.value.prefix,suffix:d.value.suffix,itemCount:d.value.itemCount,"onUpdate:page":d.value.onUpdatePage,"onUpdate:pageSize":d.value.onUpdatePageSize},null,8,["page","page-size","show-size-picker","show-quick-jumper","page-slot","page-sizes","size","show-quick-jump-dropdown","prefix","suffix","itemCount","onUpdate:page","onUpdate:pageSize"])]))}}),[["__scopeId","data-v-9bfd203a"]]),mo=Oa(i({__name:"NotificationReceiveTypeSelector",props:{modelValue:{type:Number,required:!0}},emits:["update:modelValue"],setup(e,{emit:t}){const a=t,l=[{label:no[lo.ALL],value:lo.ALL},{label:no[lo.PUBLISH],value:lo.PUBLISH},{label:no[lo.MODIFY],value:lo.MODIFY},{label:no[lo.FAVORITE],value:lo.FAVORITE},{label:no[lo.SHARE],value:lo.SHARE},{label:no[lo.CLOSE],value:lo.CLOSE}],n=e=>{a("update:modelValue",e)};return(t,a)=>(m(),d(g(me),{class:"notification-popselect",value:e.modelValue,options:l,"onUpdate:value":n,trigger:"click"},{default:v((()=>[p(g(ne),{text:"",size:"small"},{default:v((()=>{var t;return[E(" 接收类型： "+w(null==(t=l.find((t=>t.value===e.modelValue)))?void 0:t.label),1)]})),_:1})])),_:1},8,["value"]))}}),[["__scopeId","data-v-6a93ea59"]]),vo="/notifications",po=class{constructor(){t(this,"stompClient",null),t(this,"socketUrl",Gt.backend.wsURL),t(this,"subscriptions",{})}connect(){const e=new Jt(this.socketUrl,null,{transports:["websocket","xhr-streaming"]});this.stompClient=new Kt({webSocketFactory:()=>e,debug:e=>{}}),this.stompClient.onConnect=e=>{ta.info("ws connected: "+e)},this.stompClient.activate()}subscribe(e,t){if(null!==this.stompClient)if(this.stompClient.connected){const a=this.stompClient.subscribe(e,(e=>{e.body&&t(e.body)}));this.subscriptions[e]=a,ta.info(`Subscribed to: ${e}`)}else ta.info(`WebSocket is not connected, retrying to subscribe to: ${e}`),setTimeout((()=>{this.subscribe(e,t)}),1e3);else ta.warn("WebSocket client is not connected")}unsubscribe(e){const t=this.subscriptions[e];t?(t.unsubscribe(),delete this.subscriptions[e],ta.info(`Unsubscribed from: ${e}`)):ta.warn(`No subscription found for: ${e}`)}disconnect(){null!==this.stompClient&&this.stompClient.deactivate().then((()=>{ta.info("ws disconnected")}))}};t(po,"instance",new po);const go=po.instance,ho=n(null);U((()=>{const e=da.value===aa.DARK,{notification:t}=ee(["notification"],{configProviderProps:{theme:e?J:null}});ho.value=t}));const fo=new Proxy({},{get(e,t){if(!ho.value){const e=da.value===aa.DARK,{notification:t}=ee(["notification"],{configProviderProps:{theme:e?J:null}});ho.value=t}return ho.value[t]}}),wo={class:"notification-container"},yo={style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},bo="评论了：",ko=Oa(i({__name:"NotificationBtnModal",emits:["locationComment"],setup(e,{emit:t}){const a=Wn(),l=Jn(),i=n(!1),s=n(0),c=n(null),u=o((()=>da.value===aa.DARK)),f=ia.getLoginUser(),w=n((null==f?void 0:f.notificationReceiveType)??lo.FAVORITE),y=e=>{w.value=e,bl.updateNotificationReceiveType(e).then((t=>{if(t&&void 0!==t.data){const t=ia.getLoginUser();t&&(t.notificationReceiveType=e,ia.setLoginUser(t)),xa.success("通知接收类型已更新"),e!==lo.CLOSE?S():s.value=0}}))},b=()=>{ao.readAll().then((()=>{xa.success("读完了！"),S()}))},k=()=>{i.value=!0,S()};r((()=>{go.connect(),S(),x()})),z((()=>{C(),go.disconnect()}));const x=()=>{(null==f?void 0:f.id)&&go.subscribe(`/user/${f.id}${vo}`,L)},C=()=>{(null==f?void 0:f.id)&&go.unsubscribe(`/user/${f.id}${vo}`)},L=e=>{const t=na.parse(e);t&&(w.value!==lo.CLOSE&&ba("notification-received:"+t.id,(()=>{const e=t.commentId,a=t.content;let l,n=0;if(e&&a){n=a.indexOf(bo);const e=a.substring(n+4);l=qn.toJsonObject(e)}const i=fo.create({title:e&&a?a.substring(0,n+4):"发来通知~",content:()=>e?R(Ti,{fileBucket:"comment",modelValue:l,extensions:[...ki],editable:!1}):R("div",{class:u.value?"dark-notification-content":""},t.content),duration:5e3,keepAliveOnHover:!0,closable:!0,avatar:()=>R(se,{size:"small",objectFit:"cover",round:!0,src:_a.getResourceURL(t.publisherAvatar||""),class:u.value?"dark-notification-avatar":""}),action:()=>R(ne,{text:!0,type:"primary",class:u.value?"dark-notification-button":"",onClick:()=>{i.destroy(),T(t)}},["怎么个事？",R(Jl,{size:16})])})})),S())},S=()=>{ao.unreadCount().then((e=>{void 0!==(null==e?void 0:e.data)&&(s.value=w.value===lo.CLOSE?0:Number(e.data))})),i.value&&c.value&&(c.value.resetPagination(),c.value.loadNotificationPage())},T=e=>{const t=e.articleId,n=e.commentId;if(t!==a.getId){const e=uu.resolve({name:"Article",params:{articleId:t,commentId:n}}),a=window.open(e.href,"_blank");a&&a.focus()}else n&&(l.setId(n),M("locationComment",n));i.value=!1,e.isRead||ao.read(e.id)},M=t;return(e,t)=>(m(),d(g(Q),null,{default:v((()=>[h("div",wo,[p(wi,{onLongPress:b,onClick:k},{default:v((()=>[p(io,{"unread-count":Number(s.value),"notification-receive-type":w.value},null,8,["unread-count","notification-receive-type"])])),_:1}),p(g(re),{class:"notification-modal",style:{width:"600px","max-width":"100%"},show:i.value,"onUpdate:show":t[1]||(t[1]=e=>i.value=e),title:"通知列表",preset:"dialog","auto-focus":!1},{header:v((()=>[h("div",yo,[t[2]||(t[2]=h("span",null,"通知列表",-1)),p(mo,{modelValue:w.value,"onUpdate:modelValue":[t[0]||(t[0]=e=>w.value=e),y]},null,8,["modelValue"])])])),default:v((()=>[p(uo,{ref_key:"notificationListRef",ref:c,visible:i.value,onNotificationClick:T},null,8,["visible"])])),_:1},8,["show"])])])),_:1}))}}),[["__scopeId","data-v-be894cc9"]]),xo={URL:"/authentication",login:async e=>(await Sa(xo.URL+"/sessions",e)).data,register:async e=>(await Sa(xo.URL+"/accounts",e)).data,logout:async()=>(await Ea(xo.URL+"/sessions")).data,sendEmailCode:async e=>(await Sa(xo.URL+"/email-codes",e)).data,resetPassword:async e=>(await Ma(xo.URL+"/accounts/password",e)).data},Co={class:"sky"},Lo=Oa(i({__name:"ThemeToggle",setup(e){const t=o((()=>da.value===aa.DARK)),a=()=>{va()};return(e,l)=>(m(),f("div",{class:y(["theme-toggle-scene",{"is-dark":t.value}]),onClick:a},[h("div",Co,[h("div",{class:y(["sun",{"sun-set":t.value}])},[p(g(mn))],2),h("div",{class:y(["moon",{"moon-rise":t.value}])},[p(g(vn))],2)])],2))}}),[["__scopeId","data-v-1baaf921"]]),So="homeCard",To="homeSearchCondition";function Mo(e){return{id:e.id,username:e.username,phone:e.phone||"",email:e.email||"",avatar:e.avatar||"",ipLocation:e.ipLocation||"",job:e.job||"",level:e.level,notificationReceiveType:e.notificationReceiveType||0,config:e.config||{homeCard:!0}}}const Ro={class:"avatar-container"},Ao={class:"user-info"},Eo={class:"info-row"},_o={class:"info-row"},Io={class:"info-row"},zo={class:"actions-row"},Po=Oa(i({__name:"UserAvatar",setup(e){const t=n({}),a=n(null);r((()=>{bl.info().then((e=>{if(e.data){const a=Mo({id:e.data.id,username:e.data.username,phone:e.data.phone,email:e.data.email,avatar:i(e.data.avatar||""),ipLocation:e.data.ipLocation,job:e.data.job,level:e.data.level,notificationReceiveType:e.data.notificationReceiveType,createdAt:e.data.ctTm});t.value=a,ia.setLoginUser(a)}}))}));const l=()=>{var e;null==(e=a.value)||e.click()},i=e=>_a.getResourceURL(e),o=async e=>{const a=e.target;if(a.files&&a.files.length>0){const e=a.files[0];bl.changeAvatar(e).then((e=>{e.data&&(t.value.avatar=i(e.data),bl.info().then((e=>{if(e.data){const t=Mo({id:e.data.id,username:e.data.username,phone:e.data.phone,email:e.data.email,avatar:i(e.data.avatar||""),ipLocation:e.data.ipLocation,job:e.data.job,level:e.data.level,notificationReceiveType:e.data.notificationReceiveType,createdAt:e.data.ctTm});ia.setLoginUser(t)}})))}))}},s=()=>{xo.logout().then((()=>{uu.push("/login"),ia.removeLoginUser(),ia.remove(To),ia.remove(So)}))};return(e,n)=>(m(),f("div",Ro,[p(g(be),{trigger:"click",placement:"bottom"},{trigger:v((()=>[p(wi,{onLongPress:l},{default:v((()=>[p(g(se),{size:56,src:t.value.avatar,"object-fit":"cover",class:"cursor-pointer"},null,8,["src"])])),_:1})])),default:v((()=>[h("div",Ao,[h("div",Eo,[n[0]||(n[0]=h("strong",null,"手机号：",-1)),h("span",null,w(t.value.phone),1)]),h("div",_o,[n[1]||(n[1]=h("strong",null,"用户名：",-1)),h("span",null,w(t.value.username),1)]),h("div",Io,[n[2]||(n[2]=h("strong",null,"职业：",-1)),h("span",null,w(t.value.job),1)]),h("div",zo,[p(Lo),p(g(ne),{type:"error",size:"tiny",onClick:s},{default:v((()=>n[3]||(n[3]=[E("退出登录")]))),_:1})])])])),_:1}),h("input",{type:"file",ref_key:"avatarFileInputRef",ref:a,onChange:o,class:"display-none"},null,544)]))}}),[["__scopeId","data-v-0f2bd4f8"]]),Bo={class:"user-info-group"},$o={class:"online-notification-container"},Do={class:"online-info"},Uo=Oa(i({__name:"UserInfoGroup",emits:["locationComment"],setup(e,{emit:t}){const a=t;r((()=>{o()}));const l=Ye();s(l,(()=>{o()}));const i=n(0),o=()=>{bl.online().then((e=>{void 0!==(null==e?void 0:e.data)&&null!==(null==e?void 0:e.data)&&(i.value=e.data||0)}))},c=e=>{a("locationComment",e)};return(e,t)=>(m(),f("div",Bo,[h("div",$o,[p(ko,{onLocationComment:c}),h("div",Do,[E(w(i.value),1),p(g(dn),{size:20})])]),p(Po)]))}}),[["__scopeId","data-v-671fed77"]]),Vo={class:"comment-title-container"},Fo={class:"comment-header-top"},Oo={class:"comment-header-bottom"},Ho=Oa(i({__name:"CommentHeader",props:{breadcrumb:{},modelValue:{}},emits:["breadcrumbClick","locationComment","update:modelValue"],setup(e,{emit:t}){const a=t,l=e=>{a("update:modelValue",e)};return(e,t)=>(m(),f("div",Vo,[h("div",Fo,[p(g(ke),null,{default:v((()=>[(m(!0),f(L,null,S(e.breadcrumb,((e,t)=>(m(),d(g(xe),{key:t,onClick:e=>{a("breadcrumbClick",t)},class:"breadcrumb-item"},{default:v((()=>[p(g(ge),{class:"breadcrumb-text cursor-pointer"},{default:v((()=>[E(w(e.publisher)+" ",1),e.id&&e.publisherAvatar?(m(),d(g(se),{key:0,"object-fit":"cover",size:22,round:"",src:g(_a).getResourceURL(e.publisherAvatar)},null,8,["src"])):C("",!0)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}),p(Uo,{onLocationComment:t[0]||(t[0]=t=>e.$emit("locationComment"))})]),h("div",Oo,[p(g(ve),{size:"small",value:e.modelValue,"onUpdate:value":l},{default:v((()=>[p(g(pe),{value:"0"},{default:v((()=>t[1]||(t[1]=[E("热评")]))),_:1}),p(g(pe),{value:"1"},{default:v((()=>t[2]||(t[2]=[E("最新")]))),_:1}),p(g(pe),{value:"2"},{default:v((()=>t[3]||(t[3]=[E("回复")]))),_:1})])),_:1},8,["value"])])]))}}),[["__scopeId","data-v-9ae9fd75"]]),jo={class:"comment-controls-container"},No={class:"comment-reply-info"},qo={class:"comment-interaction-btn"},Yo=Oa(i({__name:"CommentControls",props:{comment:{},showReplyListBtn:{type:Boolean}},emits:["showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn"],setup:(e,{emit:t})=>(e,t)=>(m(),f("div",jo,[h("div",No,[$(p(g(ne),{class:"comment-reply-list-btn",style:{"margin-left":"3%"},text:"",type:"info",onClick:t[0]||(t[0]=t=>e.$emit("showReplyList",e.comment))},{default:v((()=>t[5]||(t[5]=[E(" 回复列表> ")]))),_:1},512),[[D,e.showReplyListBtn&&!e.comment.fixed]]),p(g(xn),{onClick:t[1]||(t[1]=t=>e.$emit("handleCommentReplyClick",e.comment)),class:"cursor-pointer",size:20}),E(" "+w(e.comment.replyCount),1)]),h("div",qo,[p(g(Gl),{color:e.comment.isLike?"var(--blue)":"",class:"cursor-pointer",size:16,onClick:t[2]||(t[2]=t=>e.$emit("interactionBtn",e.comment,1))},null,8,["color"]),E(" "+w(e.comment.likeCount)+" ",1),p(g(Zl),{color:e.comment.isDislike?"var(--blue)":"",class:"cursor-pointer",size:16,onClick:t[3]||(t[3]=t=>e.$emit("interactionBtn",e.comment,0))},null,8,["color"]),E(" "+w(e.comment.dislikeCount)+" ",1),p(g(kn),{color:e.comment.isFavorite?"var(--blue)":"",class:"cursor-pointer",size:18,onClick:t[4]||(t[4]=t=>e.$emit("favoriteBtn",e.comment))},null,8,["color"]),E(" "+w(e.comment.favoriteCount),1)])]))}),[["__scopeId","data-v-d5ab7a07"]]),Wo={class:"user-info-row"},Jo={class:"user-detail-col"},Ko={class:"user-nickname"},Xo={class:"user-extra-info"},Go={class:"comment-content-row"},Zo={class:"comment-reply-row"},Qo=Oa(i({__name:"CommentListItem",props:{comment:{},flashCommentId:{},showReplyListBtn:{type:Boolean},commentInputVisible:{},quickReplyLoading:{}},emits:["showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn","quickReplyComment","updateEditor"],setup(e,{emit:t}){const a=t,l=[...ki,"characterCount"];return(e,t)=>(m(),f("div",{class:y({"user-comment-container-fixed":e.comment.fixed,"user-comment-container":!e.comment.fixed,"comment-flash":e.flashCommentId===e.comment.id})},[h("div",Wo,[p(g(se),{round:"",size:"large","object-fit":"cover",src:e.comment.publisherAvatar?g(_a).getResourceURL(e.comment.publisherAvatar):""},null,8,["src"]),h("div",Jo,[h("span",Ko,w(e.comment.publisher),1),h("span",Xo,[E(w(e.comment.publisherJob)+" | ",1),h("span",{class:"time-clickable",onClick:t[0]||(t[0]=t=>{var a;void 0===(a=e.comment).showExactTime?a.showExactTime=!0:a.showExactTime=!a.showExactTime})},w(e.comment.showExactTime?e.comment.exactPublishedAt:e.comment.publishedAt),1),E(" | "+w(e.comment.ipLocation),1)])])]),h("div",Go,[p(Ti,{"file-bucket":g(oo),modelValue:e.comment.contentObj,"onUpdate:modelValue":t[1]||(t[1]=t=>e.comment.contentObj=t),extensions:[...g(ki)],editable:!1},null,8,["file-bucket","modelValue","extensions"])]),p(Yo,{comment:e.comment,showReplyListBtn:e.showReplyListBtn,onShowReplyList:t[2]||(t[2]=t=>e.$emit("showReplyList",e.comment)),onHandleCommentReplyClick:t[3]||(t[3]=t=>e.$emit("handleCommentReplyClick",e.comment)),onInteractionBtn:t[4]||(t[4]=(t,a)=>e.$emit("interactionBtn",t,a)),onFavoriteBtn:t[5]||(t[5]=t=>e.$emit("favoriteBtn",e.comment))},null,8,["comment","showReplyListBtn"]),p(V,{name:"comment-reply",appear:""},{default:v((()=>[$(h("div",Zo,[p(Ti,{modelValue:e.comment.quickCommentReply,"onUpdate:modelValue":t[6]||(t[6]=t=>e.comment.quickCommentReply=t),class:"comment-reply-tiptap-editor",ref:t=>t&&((e,t)=>{t&&"editor"in t&&a("updateEditor",e,t.editor)})(e.comment.id,t),"editor-props":{attributes:{class:"ProseMirrorInput","data-comment-id":e.comment.id}},"file-bucket":g(oo),placeholder:"说是你的自由，但是...","show-character-count":!0,extensions:l,toolbar:!0,onKeydown:t[7]||(t[7]=F(P((t=>e.$emit("quickReplyComment",e.comment)),["alt","prevent"]),["enter"]))},null,8,["modelValue","editor-props","file-bucket"]),p(g(ne),{class:"comment-reply-send-btn",text:"",type:"info",loading:!(!e.quickReplyLoading||!e.comment.id)&&e.quickReplyLoading.get(e.comment.id),onClick:t[8]||(t[8]=t=>e.$emit("quickReplyComment",e.comment))},{default:v((()=>[p(g(hn),{size:28})])),_:1},8,["loading"])],512),[[D,e.commentInputVisible===e.comment.id]])])),_:1})],2))}}),[["__scopeId","data-v-8bcd7388"]]),er={class:"comment-scroll"},tr={class:"comment-list-footer"},ar=Oa(i({__name:"CommentList",props:{commentList:{},flashCommentId:{},showReplyListBtn:{type:Boolean},commentInputVisible:{},commentScrollTrigger:{},commentLoading:{type:Boolean},commentNoMore:{type:Boolean},hasCommentPermission:{type:Boolean},quickReplyLoading:{}},emits:["loadMoreComments","showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn","quickReplyComment","updateEditor","updateCommentRef"],setup(e,{expose:t,emit:a}){const l=n(null),i=e,o=a,r=(e,t)=>{o("interactionBtn",e,t)},s=(e,t)=>{o("updateEditor",e,t)},c=()=>{0===i.commentList.length&&i.commentNoMore||o("loadMoreComments")};return t({commentListContainerRef:l}),(e,t)=>(m(),f("div",{ref_key:"commentListContainerRef",ref:l,class:y(["comment-list-container",{"has-input-box":"-1"==e.commentInputVisible}])},[p(g(Ce),{onLoad:c,distance:50,trigger:e.commentScrollTrigger},{default:v((()=>[h("div",er,[p(O,{name:"smooth",appear:"",tag:"div"},{default:v((()=>[(m(!0),f(L,null,S(e.commentList,(a=>(m(),f("div",{key:a.id,ref_for:!0,ref:e=>{e&&((e,t)=>{o("updateCommentRef",e,t)})(a.id,e)}},[p(Qo,{comment:a,"flash-comment-id":e.flashCommentId,"show-reply-list-btn":e.showReplyListBtn,"comment-input-visible":e.commentInputVisible,"quick-reply-loading":e.quickReplyLoading,onShowReplyList:t[0]||(t[0]=t=>e.$emit("showReplyList",t)),onHandleCommentReplyClick:t[1]||(t[1]=t=>e.$emit("handleCommentReplyClick",t)),onInteractionBtn:r,onFavoriteBtn:t[2]||(t[2]=t=>e.$emit("favoriteBtn",t)),onQuickReplyComment:t[3]||(t[3]=t=>e.$emit("quickReplyComment",t)),onUpdateEditor:s},null,8,["comment","flash-comment-id","show-reply-list-btn","comment-input-visible","quick-reply-loading"])])))),128))])),_:1}),h("div",tr,[e.commentLoading?(m(),d(g(Le),{key:0,class:"display-flex"})):e.commentLoading||!e.commentNoMore&&0!==e.commentList.length?C("",!0):(m(),d(g(Se),{key:1,description:e.hasCommentPermission?"没有更多评论了...":"您没有权限查看评论"},null,8,["description"]))])])])),_:1},8,["trigger"])],2))}}),[["__scopeId","data-v-abaa15ee"]]),lr=Oa(i({__name:"CommentMainInput",props:{commentReply:{},sendCommentLoading:{type:Boolean},disabled:{type:Boolean}},emits:["sendComment","update:commentReply"],setup(e,{expose:t,emit:a}){const l=a,i=n(),o=n(),r=[...ki,"characterCount"];return t({commentInputWrapperRef:i,sendTiptapEditorRef:o}),(e,t)=>(m(),d(g(Te),{bottom:0,style:{"z-index":"1500"},class:"comment-input-affix"},{default:v((()=>[h("div",{class:"comment-input-row",ref_key:"commentInputWrapperRef",ref:i},[p(Ti,{ref_key:"sendTiptapEditorRef",ref:o,"model-value":e.commentReply,"onUpdate:modelValue":t[0]||(t[0]=e=>l("update:commentReply",e)),class:"comment-tiptap-editor","editor-props":{attributes:{class:"ProseMirrorInput","data-main-editor":"true"}},"file-bucket":g(oo),placeholder:"说是你的自由，但是...","show-character-count":!0,extensions:r,toolbar:!0,onKeydown:t[1]||(t[1]=F(P((t=>e.$emit("sendComment")),["alt","prevent"]),["enter"]))},null,8,["model-value","file-bucket"]),p(g(ne),{text:"",type:"info",loading:e.sendCommentLoading,onClick:t[2]||(t[2]=t=>e.$emit("sendComment")),class:"comment-reply-send-btn",size:"small",disabled:e.disabled},{default:v((()=>[p(g(fn),{size:28})])),_:1},8,["loading","disabled"])],512)])),_:1}))}}),[["__scopeId","data-v-0128642d"]]);function nr(e,t){const a=n(0),l=n(5),i=n(!0),c=()=>{if(t.value){const e=t.value.clientHeight,n=Math.ceil(e/250),i=Math.max(3,n);a.value===e&&l.value===i||(a.value=e,l.value=i,ta.debug("评论容器高度更新:",{containerHeight:a.value,commentsPerScreen:l.value,commentHeight:250,calculatedFromHeight:n}))}},u=o((()=>{let t;return i.value||0===e.value.length?(t=Math.ceil(1.2*l.value),t=Math.max(5,t)):t=l.value<=4?Math.max(3,Math.ceil(.8*l.value)):l.value<=8?Math.max(4,Math.ceil(.6*l.value)):Math.max(6,Math.ceil(.5*l.value)),ta.debug("计算评论加载量:",{size:t,isInitialLoad:i.value,commentsPerScreen:l.value,currentCommentsCount:e.value.length,containerHeight:a.value}),t})),d=n(null),m=()=>{d.value&&(d.value.disconnect(),d.value=null)},v=()=>{c()};return s(t,(e=>{m(),e&&(c(),t.value&&window.ResizeObserver&&(d.value=new ResizeObserver((()=>{c()})),d.value.observe(t.value)))}),{immediate:!0}),r((()=>{window.addEventListener("resize",v),c()})),z((()=>{window.removeEventListener("resize",v),m()})),{containerHeight:x(a),commentsPerScreen:x(l),isInitialLoad:x(i),calculatedLoadSize:u,updateContainerHeight:c,markInitialLoadComplete:()=>{i.value&&(ta.debug("评论首次加载完成，切换到后续加载模式"),i.value=!1)},resetToInitialLoad:()=>{ta.debug("重置评论加载状态为初始加载模式"),i.value=!0},COMMENT_HEIGHT:250}}const ir={URL:"/core/favorites",save:async e=>(await Sa(ir.URL,e)).data,toggle:async e=>(await Sa(ir.URL,e)).data},or={URL:"/core/interactions",save:async e=>(await Sa(or.URL,e)).data,toggle:async e=>(await Sa(or.URL,e)).data};const rr={URL:"/core/comments",search:async(e,t)=>{const a={signal:t};return(await La(rr.URL+"/search",e,a)).data},location:async e=>(await La(rr.URL+"/"+e+"/location")).data,loadById:async e=>(await La(rr.URL+"/"+e)).data,load:async e=>(await La(rr.URL+e)).data,save:async e=>(await Sa(rr.URL,e)).data};function sr(e,t,a,l,n,i){return{clearAllQuickReplyContent:()=>(a.value="-1",({commentList:e})=>{e.forEach((e=>{e.quickCommentReply&&(e.quickCommentReply=void 0);const t=n.value.get(e.id);t&&t.commands&&t.commands.clearContent()}))}),handleCommentReplyClick:(e,{isLastBreadcrumb:t})=>{a.value!==e.id?(a.value=e.id,c((()=>{var a,l;if(t&&!e.fixed){const t=n.value.get(e.id);if(t&&(!e.quickCommentReply||!(null==(l=null==(a=e.quickCommentReply.content)?void 0:a[0])?void 0:l.content))){const a={type:"doc",content:[{type:"paragraph",content:[{type:"mention",attrs:{id:e.publisher,label:e.publisher,avatar:e.publisherAvatar||""}},{type:"text",text:" "}]}]};t.commands.setContent(a)}}}))):a.value="-1"},debouncedQuickReplyComment:(t,a)=>{ba(`comment-quick-reply-${t.id}`,(()=>{((t,{isLastBreadcrumb:a,onSuccess:l})=>{if(!Ii(e.value,t.id))return;const o=n.value.get(t.id),r={value:t.quickCommentReply},s=_i(o,r,"啥也没有可不能发送哦~");if(!s.isValid)return;t.quickCommentReply=s.content||r.value,zi(e.value,!0,t.id);const c=qn.toJsonString(t.quickCommentReply);rr.save({content:c,articleId:i(),parentCommentId:a&&!t.fixed?t.parentCommentId:t.id}).then((e=>{xa.success("发送成功"),(null==e?void 0:e.data)&&l&&l(e.data),o&&o.commands&&o.commands.clearContent(),t.quickCommentReply=void 0})).finally((()=>{e.value.set(t.id,!1)}))})(t,a)}),300)},debouncedSendComment:(e,a)=>{ba("comment-send",(()=>{((e,{lastBreadcrumbComment:a,onSuccess:n})=>{if(!Ii(t))return;const o=null==e?void 0:e.sendTiptapEditorRef,r=_i(o,{value:l.value},"啥也没有可不能发送哦~");if(!r.isValid)return;l.value=r.content||l.value,zi(t,!0);const s=qn.toJsonString(l.value),c=a,u=i();rr.save({content:s,articleId:u,parentCommentId:c.id}).then((e=>{xa.success("发送成功");const t=null==e?void 0:e.data;t&&n&&n(String(t)),null==o||o.clearContent(),l.value=void 0})).finally((()=>{t.value=!1}))})(e,a)}),300)}}}function cr(e){const{quickReplyLoading:t,sendCommentLoading:a,commentInputVisible:l,commentReply:i,quickReplyTiptapEditorMap:o,updateEditor:r}=function(){const e=n(new Map),t=n(!1),a=n("-1"),l=n(void 0),i=n(new Map);return{quickReplyLoading:e,sendCommentLoading:t,commentInputVisible:a,commentReply:l,quickReplyTiptapEditorMap:i,updateEditor:(e,t)=>{i.value.set(e,t)}}}(),{interactionBtn:s,favoriteBtn:c}={interactionBtn:(e,t)=>{const a={targetType:0,targetId:e.id,actionType:t};or.save({...a,type:a.targetType}).then((a=>{const l=null==a?void 0:a.data;if(l){e.likeCount=l.likeCount,e.dislikeCount=l.dislikeCount;const a=1===t;l.cancel?a?(xa.info("赞取消"),e.isLike=!1):(xa.info("踩取消"),e.isDislike=!1):a?(xa.success("赞 :)"),e.isLike=!0):(xa.warning("踩 :("),e.isDislike=!0)}}))},favoriteBtn:e=>{const t={targetType:0,targetId:e.id};ir.save(t).then((t=>{const a=null==t?void 0:t.data;a&&(e.favoriteCount=a.count,a.cancel?(xa.info("取消收藏"),e.isFavorite=!1):(xa.success("已收藏"),e.isFavorite=!0))}))}},{clearAllQuickReplyContent:u,handleCommentReplyClick:d,debouncedQuickReplyComment:m,debouncedSendComment:v}=sr(t,a,l,i,o,e);return{quickReplyLoading:t,sendCommentLoading:a,commentInputVisible:l,commentReply:i,quickReplyTiptapEditorMap:o,updateEditor:r,interactionBtn:s,favoriteBtn:c,clearAllQuickReplyContent:u,handleCommentReplyClick:d,debouncedQuickReplyComment:m,debouncedSendComment:v}}function ur(e,t,a,l,i){const o=n(new Map),r=n("0"),s=n(""),u=Jn(),d=Ye(),m=We(),v=()=>u.getId,p=e=>{a(),l(e)},g=e=>{c((()=>{const t=o.value.get(e);t&&(t.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout((()=>{t.scrollIntoView({behavior:"smooth",block:"center"}),(e=>{s.value=e,setTimeout((()=>{s.value=""}),1e3)})(e)}),10))}))};return{commentRefs:o,commentScrollTrigger:r,flashCommentId:s,updateCommentRef:(e,t)=>{o.value.set(e,t)},locationComment:(l=v())=>{e.value=!0,l&&(u.setId(l),m.push({params:{...d.params,commentId:l}}),rr.location(l).then((e=>{const a=null==e?void 0:e.data;if(a){const e=a.parents,n=a.comments;p(n),t.resetBreadcrumb();const o=e.length;if(o>0){for(let l=o-1;l>=0;l--){const a=e[l];t.addBreadcrumb(a)}const a=e[0];i(a)}g(l)}})).catch((e=>{e.response&&403===e.response.status&&a()})).finally((()=>{e.value=!1})))},scrollToComment:g,prepareScrollToComment:p,resetScrollTrigger:()=>{r.value=String(Date.now())},getCommentId:v}}function dr(e,t,a){const l=Jn(),i=Ye(),r=function(){const e=n([{id:"",publisher:"评论列表"}]),t=o((()=>3===e.value.length)),a=o((()=>e.value.length-1)),l=o((()=>e.value[e.value.length-1])),i=n(!0);return{breadcrumb:e,isLastBreadcrumb:t,lastBreadcrumbIndex:a,lastBreadcrumbComment:l,showReplyListBtn:i,resetBreadcrumb:()=>{e.value=[{id:"",publisher:"评论列表"}]},addBreadcrumb:t=>{e.value.push(t)},setShowReplyListBtn:()=>{i.value=a.value<2}}}(),c=function(e,t,a,l){const i=n([]),o=n(!1),r=n(!1),s=n(!1),c=n("0"),u=()=>{i.value=[],o.value=!1,r.value=!1},d=e=>{e&&0!==e.length&&e.map((({...e})=>({...e,publishedAt:ro.getRelativeTime(e.publishedAt),exactPublishedAt:ro.toTimeString(e.publishedAt),fixed:!1}))).forEach((e=>{e.contentObj=qn.toJsonObject(e.content),e.quickCommentReply=void 0,i.value.push(e)}))},m=e=>{i.value.unshift({...e,publishedAt:ro.getRelativeTime(e.publishedAt),exactPublishedAt:ro.toTimeString(e.publishedAt),fixed:!0,contentObj:qn.toJsonObject(e.content)})},v=async(e,t=!1)=>{if(l&&l.lastBreadcrumbIndex.value>0&&(0==i.value.length||!i.value[0].fixed))if(t){const t=await rr.loadById(e.id);(null==t?void 0:t.data)&&m(t.data)}else m(e)},p=n=>{if(o.value||r.value)return;o.value=!0;const u=t?t():5;ka("article-comments",(()=>{const t=i.value.length>0?i.value[i.value.length-1].id:"";r.value?o.value=!1:Promise.allSettled([rr.load(`?articleId=${e()}&id=${t}&parentCommentId=${null==n?void 0:n.id}&loadSize=${u}&sortType=${c.value}`).catch((e=>{if(e.response&&403===e.response.status)throw s.value=!1,o.value=!1,e;throw e})),v(n,!0)]).then((e=>{var t,a;if("fulfilled"===e[0].status&&(null==(t=e[0])?void 0:t.value)){const t=(null==(a=e[0])?void 0:a.value).data;if(0==(null==t?void 0:t.length))return void(r.value=!0);t.length<u&&(r.value=!0),d(t),l&&l.setShowReplyListBtn()}else r.value=!0})).catch((e=>{r.value=!1})).finally((()=>{o.value=!1,a&&a()}))}),300)};return{commentList:i,commentLoading:o,commentNoMore:r,hasCommentPermission:s,sortType:c,resetCommentList:u,loadCurrentCommentList:(e=!0)=>{if(!o.value||e){if(r.value&&!e){if(0!==i.value.length)return;r.value=!1}e&&u(),l&&p(l.lastBreadcrumbComment.value)}},loadCommentList:p,addCommentList:d,setFirstFixedComment:v,initCommentPermission:()=>{s.value=!0}}}(e,t,a,r),u=ur(c.commentLoading,r,c.resetCommentList,c.addCommentList,c.setFirstFixedComment),d=c.resetCommentList;return c.resetCommentList=()=>{u.commentRefs.value.clear(),d(),u.resetScrollTrigger()},s(i,(()=>{u.getCommentId()?u.locationComment():c.loadCurrentCommentList()})),s((()=>l.getId),(e=>{e&&u.locationComment(e)})),{...r,...c,...u,commentStore:l}}const mr=Oa(i({__name:"CommentInfo",props:{articleId:{type:Function,required:!0}},emits:["sendEnd","quickReplyEnd"],setup(e,{expose:t,emit:a}){const l=e,i=a,u=n(0),d=n(),h=n(),w=n(),y=o((()=>{var e;return(null==(e=w.value)?void 0:e.commentListContainerRef)||null})),b=nr(o((()=>k.commentList.value)),y),k=dr(l.articleId,(()=>b.calculatedLoadSize.value),(()=>b.markInitialLoadComplete())),x=cr(l.articleId),C=()=>{c((()=>{var e;const t=document.querySelector(".comment-list-container");if(t){const a=window.innerWidth<=768,l="-1"!==x.commentInputVisible.value,n="-1"===x.commentInputVisible.value;a?t.style.paddingBottom=l?"20px":"150px":l?t.style.paddingBottom="1.25rem":n&&(null==(e=h.value)?void 0:e.commentInputWrapperRef)?(u.value=h.value.commentInputWrapperRef.offsetHeight||0,t.style.paddingBottom=`${u.value+12}px`):t.style.paddingBottom="1.25rem"}}))};r((()=>{k.initCommentPermission(),L(),window.addEventListener("resize",L);const e=k.getCommentId();e?k.locationComment(e):k.loadCurrentCommentList()})),M((()=>{window.removeEventListener("resize",L);const e=document.body,t=document.querySelector(".comment-info-container");e.classList.remove("comment-reply-active"),t&&t.classList.remove("has-quick-reply")}));s((()=>x.commentInputVisible.value),(e=>{const t=document.body,a=document.querySelector(".comment-info-container");"-1"!==e?(t.classList.add("comment-reply-active"),a&&a.classList.add("has-quick-reply")):(t.classList.remove("comment-reply-active"),a&&a.classList.remove("has-quick-reply")),C(),setTimeout((()=>{C()}),350)})),s((()=>k.hasCommentPermission.value),(e=>{e&&c(C)})),s(da,(()=>{c((()=>{document.querySelectorAll(".ProseMirror, .editor-content, .tiptap-editor-wrapper").forEach((e=>{if(e instanceof HTMLElement){const t=e.style.backgroundColor;e.style.backgroundColor="transparent",e.offsetHeight,e.style.backgroundColor=t}}))}))})),s((()=>x.commentReply.value),(e=>{var t;if(e&&e.content&&Array.isArray(e.content)){(null==(t=e.content)?void 0:t.some((e=>!(!e.content||!Array.isArray(e.content))&&e.content.some((e=>!("text"!==e.type||!e.text||!e.text.trim())||"text"!==e.type)))))||(x.commentReply.value=void 0)}}));const L=()=>{c((()=>{var e;if(d.value&&(null==(e=h.value)?void 0:e.commentInputWrapperRef)){const e=d.value.offsetWidth;h.value.commentInputWrapperRef.style.width=`${e}px`,C()}}))},S=e=>{k.resetCommentList(),b.resetToInitialLoad(),e!==k.lastBreadcrumbIndex.value&&k.breadcrumb.value.splice(e+1);x.clearAllQuickReplyContent()({commentList:k.commentList.value}),k.loadCommentList(k.breadcrumb.value[e])},T=e=>{k.resetCommentList(),b.resetToInitialLoad();x.clearAllQuickReplyContent()({commentList:k.commentList.value}),k.addBreadcrumb(e),k.loadCommentList(e)},R=e=>{x.handleCommentReplyClick(e,{isLastBreadcrumb:k.isLastBreadcrumb.value})},A=e=>{x.debouncedQuickReplyComment(e,{isLastBreadcrumb:k.isLastBreadcrumb.value,onSuccess:e=>{k.locationComment(e),x.commentInputVisible.value="-1"}}),i("quickReplyEnd")},E=()=>{x.debouncedSendComment(h.value,{lastBreadcrumbComment:k.lastBreadcrumbComment.value,onSuccess:e=>{k.locationComment(e)}}),i("sendEnd")},_=e=>{k.sortType.value=e,k.resetCommentList(),b.resetToInitialLoad(),k.loadCurrentCommentList()};return t({loadCurrentCommentList:k.loadCurrentCommentList}),(e,t)=>(m(),f("div",{ref_key:"commentInfoRef",ref:d,class:"comment-info-container"},[p(Ho,{breadcrumb:g(k).breadcrumb.value,"model-value":g(k).sortType.value,"onUpdate:modelValue":_,onBreadcrumbClick:S,onLocationComment:g(k).locationComment},null,8,["breadcrumb","model-value","onLocationComment"]),p(ar,{ref_key:"commentListRef",ref:w,"comment-list":g(k).commentList.value,"flash-comment-id":g(k).flashCommentId.value,"show-reply-list-btn":g(k).showReplyListBtn.value,"comment-input-visible":g(x).commentInputVisible.value,"comment-scroll-trigger":g(k).commentScrollTrigger.value,"comment-loading":g(k).commentLoading.value,"comment-no-more":g(k).commentNoMore.value,"has-comment-permission":g(k).hasCommentPermission.value,"quick-reply-loading":g(x).quickReplyLoading.value,onLoadMoreComments:t[0]||(t[0]=e=>g(k).loadCurrentCommentList(!1)),onShowReplyList:T,onHandleCommentReplyClick:R,onInteractionBtn:g(x).interactionBtn,onFavoriteBtn:g(x).favoriteBtn,onQuickReplyComment:A,onUpdateEditor:g(x).updateEditor,onUpdateCommentRef:g(k).updateCommentRef},null,8,["comment-list","flash-comment-id","show-reply-list-btn","comment-input-visible","comment-scroll-trigger","comment-loading","comment-no-more","has-comment-permission","quick-reply-loading","onInteractionBtn","onFavoriteBtn","onUpdateEditor","onUpdateCommentRef"]),p(V,{name:"slide-up",appear:""},{default:v((()=>[$(p(lr,{"comment-reply":g(x).commentReply.value,"onUpdate:commentReply":t[1]||(t[1]=e=>g(x).commentReply.value=e),"send-comment-loading":g(x).sendCommentLoading.value,onSendComment:E,ref_key:"commentMainInputRef",ref:h},null,8,["comment-reply","send-comment-loading"]),[[D,g(k).hasCommentPermission.value&&"-1"==g(x).commentInputVisible.value]])])),_:1})],512))}}),[["__scopeId","data-v-f96c628a"]]);function vr(){const e=Wn(),t=Ye(),a=n({}),l=n(!0),i=()=>e.getId,o=()=>{l.value=!0;const e=i();e&&Yn.detail(e).then((e=>{const t=null==e?void 0:e.data;t&&(a.value=function(e){return{id:e.id,title:e.title,tags:e.tag?e.tag.split(","):[],tag:e.tag||"",operationLevel:e.operationLevel,publishedScope:e.publishedScope,content:e.content,contentObj:qn.toJsonObject(e.content),publisher:e.publisher,publisherAvatar:e.publisherAvatar||"",isOwner:e.isOwner,ipLocation:e.ipLocation,publishedAt:e.publishedAt?ro.getRelativeTime(e.publishedAt):"",likeCount:e.likeCount,isLike:e.isLike,dislikeCount:e.dislikeCount,isDislike:e.isDislike,favoriteCount:e.favoriteCount,isFavorite:e.isFavorite,commentCount:e.commentCount,lastModified:e.lastModified?ro.getRelativeTime(e.lastModified):"",exactPublishedAt:e.publishedAt?ro.toTimeString(e.publishedAt):"",exactLastModified:e.lastModified?ro.toTimeString(e.lastModified):"",shareUsers:e.shareUsers}}(t),l.value=!1),ta.debug("article detail: ",a.value)})).catch((e=>{l.value=!1,e.response&&403===e.response.status?(xa.error("哎呀，您没有权限查看这篇文章"),uu.push("/")):xa.error("加载文章失败，请稍后重试")}))};return{article:a,articleLoading:l,getArticleId:i,loadArticleDetail:o,loadArticleDetailCount:()=>{const e=i();e&&Yn.detail(e).then((e=>{const t=null==e?void 0:e.data;t&&(a.value.likeCount=t.likeCount,a.value.dislikeCount=0,a.value.favoriteCount=0,a.value.commentCount=t.commentCount)}))},backHome:()=>{uu.push("/")},initialize:()=>{r((()=>{o()})),s(t,((e,t)=>{var a;e.params.articleId!==(null==(a=null==t?void 0:t.params)?void 0:a.articleId)&&o()})),s(da,(()=>{c((()=>{if(!l.value){const e=document.querySelector(".article-content .ProseMirror");e instanceof HTMLElement&&(e.classList.add("theme-priority"),e.offsetHeight)}}))}))}}}const pr={class:"article-layout"},gr={key:0,class:"article-info-container"},hr={class:"article-header"},fr={class:"article-header-content-wrapper"},wr={class:"article-header-content"},yr={class:"article-tag-container"},br={class:"flex-column-start"},kr={class:"action-buttons-container"},xr={class:"edit-button-container"},Cr={class:"interaction-container"},Lr={class:"comment-count-container"},Sr={class:"article-content"},Tr={style:{"padding-right":"1rem"}},Mr=Oa(i({__name:"Article",props:{articleId:{default:void 0}},emits:["article-edit-success","back-to-home"],setup(e,{expose:t,emit:a}){const l=a,i=vr(),o=(r=i.article,{interactionBtn:(e,t)=>{const a={targetType:1,targetId:e,actionType:t};or.save({...a,type:a.targetType}).then((e=>{const a=null==e?void 0:e.data;if(a){const e=a;r.value.likeCount=e.likeCount||0,r.value.dislikeCount=e.dislikeCount||0;const l=1===t;e.cancel?l?(xa.info("赞取消"),r.value.isLike=!1):(xa.info("踩取消"),r.value.isDislike=!1):l?(xa.success("赞 :)"),r.value.isLike=!0):(xa.warning("踩 :("),r.value.isDislike=!0)}}))},favoriteBtn:e=>{const t={targetType:1,targetId:e};ir.save(t).then((e=>{const t=null==e?void 0:e.data;if(t){const e=t;r.value.favoriteCount=e.count||0,e.cancel?(xa.info("取消收藏"),r.value.isFavorite=!1):(xa.success("已收藏"),r.value.isFavorite=!0)}}))}});var r;const s=function(e){return{toggleTimeFormat:t=>{"publish"===t?void 0===e.value.showExactPublishTime?e.value.showExactPublishTime=!0:e.value.showExactPublishTime=!e.value.showExactPublishTime:void 0===e.value.showExactModifyTime?e.value.showExactModifyTime=!0:e.value.showExactModifyTime=!e.value.showExactModifyTime}}}(i.article),c=n(),u=n();i.initialize();const y=()=>{c.value&&i.article.value&&c.value.openEditArticleDialog(i.article.value)},b=()=>{i.loadArticleDetail(),u.value&&u.value.loadCurrentCommentList(),i.article.value&&l("article-edit-success",i.article.value)};return t({reloadArticleDetail:async()=>{await i.loadArticleDetail()},getCurrentArticle:()=>i.article.value}),(e,t)=>(m(),f("div",pr,[p(to,{show:g(i).articleLoading.value},null,8,["show"]),g(i).articleLoading.value?C("",!0):(m(),f("div",gr,[h("div",hr,[h("div",fr,[h("div",wr,[h("h2",null,w(g(i).article.value.title),1),h("div",yr,[(m(!0),f(L,null,S(g(i).article.value.tags,(e=>(m(),d(g(Me),{class:"article-tag",key:e,type:"primary"},{default:v((()=>[E(w(e),1)])),_:2},1024)))),128))])])]),h("div",br,[p(g(ge),{type:"info",class:"display-block time-clickable",onClick:t[0]||(t[0]=e=>g(s).toggleTimeFormat("publish"))},{default:v((()=>[E(" 发布时间："+w(g(i).article.value.showExactPublishTime?g(i).article.value.exactPublishedAt:g(i).article.value.publishedAt),1)])),_:1}),p(g(ge),{type:"info",class:"display-block time-clickable",onClick:t[1]||(t[1]=e=>g(s).toggleTimeFormat("modify"))},{default:v((()=>[E(" 最近修改："+w(g(i).article.value.showExactModifyTime?g(i).article.value.exactLastModified:g(i).article.value.lastModified),1)])),_:1}),p(g(ge),{type:"info",class:"display-block"},{default:v((()=>[E(" 拥有者："+w(g(i).article.value.publisher)+" | 等级："+w(g(i).article.value.operationLevel)+" | ip: "+w(g(i).article.value.ipLocation),1)])),_:1})]),h("div",kr,[h("div",xr,[g(i).article.value.isOwner?(m(),d(g(bn),{key:0,class:"cursor-pointer",size:28,onClick:y})):C("",!0),p(g(Xl),{class:"cursor-pointer",size:28,onClick:g(i).backHome},null,8,["onClick"])]),h("div",Cr,[p(g(Gl),{color:g(i).article.value.isLike?"var(--blue)":"",size:20,class:"cursor-pointer",onClick:t[2]||(t[2]=e=>g(o).interactionBtn(g(i).article.value.id,1))},null,8,["color"]),E(" "+w(g(i).article.value.likeCount)+" ",1),p(g(Zl),{color:g(i).article.value.isDislike?"var(--blue)":"",size:20,class:"cursor-pointer",onClick:t[3]||(t[3]=e=>g(o).interactionBtn(g(i).article.value.id,0))},null,8,["color"]),E(" "+w(g(i).article.value.dislikeCount)+" ",1),p(g(kn),{color:g(i).article.value.isFavorite?"var(--blue)":"",size:20,class:"cursor-pointer",onClick:t[4]||(t[4]=e=>g(o).favoriteBtn(g(i).article.value.id))},null,8,["color"]),E(" "+w(g(i).article.value.favoriteCount),1)]),h("div",Lr,[p(g(Ql),{size:20}),E(w(g(i).article.value.commentCount),1)])])]),h("div",Sr,[p(g(ae),null,{default:v((()=>[h("div",Tr,[p(Ti,{modelValue:g(i).article.value.contentObj,"onUpdate:modelValue":t[5]||(t[5]=e=>g(i).article.value.contentObj=e),editable:!1,"file-bucket":g(Vi),"all-extensions":!0,"character-limit":g(xi),"use-thumbnail":!0},null,8,["modelValue","file-bucket","character-limit"])])])),_:1})])])),p(Ni,{ref_key:"articleModalRef",ref:c,onSuccess:b},null,512),p(mr,{ref_key:"commentInfoRef",ref:u,articleId:g(i).getArticleId,onQuickReplyEnd:g(i).loadArticleDetailCount,onSendEnd:g(i).loadArticleDetailCount},null,8,["articleId","onQuickReplyEnd","onSendEnd"])]))}}),[["__scopeId","data-v-c7492ac5"]]),Rr={},Ar=function(e,t,a){let l=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),n=(null==a?void 0:a.nonce)||(null==a?void 0:a.getAttribute("nonce"));l=e(t.map((e=>{if((e=function(e){return"/"+e}(e))in Rr)return;Rr[e]=!0;const t=e.endsWith(".css"),a=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${a}`))return;const l=document.createElement("link");return l.rel=t?"stylesheet":"modulepreload",t||(l.as="script"),l.crossOrigin="",l.href=e,n&&l.setAttribute("nonce",n),document.head.appendChild(l),t?new Promise(((t,a)=>{l.addEventListener("load",t),l.addEventListener("error",(()=>a(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return l.then((t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)}))},Er={class:"article-header"},_r={class:"flex-between-center"},Ir={class:"article-content"},zr=Oa(i({__name:"ArticleCard",props:{article:{},index:{},cardColor:{},isDragging:{type:Boolean},draggedArticle:{},dragOverCardId:{},dragOverPosition:{},isSingleCardRow:{type:Boolean},dragStyle:{}},emits:["toggleScope","startLongPress","cancelLongPress","download","setEditor"],setup(e,{emit:t}){const a=e,l=t,n=o((()=>{var e;const t=a.isDragging&&(null==(e=a.draggedArticle)?void 0:e.id)===a.article.id,l=a.isDragging&&a.dragOverCardId===a.article.id;return{dragging:t,"drag-over-before":l&&"before"===a.dragOverPosition&&!a.isSingleCardRow,"drag-over-after":l&&"after"===a.dragOverPosition&&!a.isSingleCardRow,"drag-over-before-vertical":l&&"before"===a.dragOverPosition&&a.isSingleCardRow,"drag-over-after-vertical":l&&"after"===a.dragOverPosition&&a.isSingleCardRow}})),i=o((()=>{var e;return{backgroundColor:a.cardColor,...a.isDragging&&(null==(e=a.draggedArticle)?void 0:e.id)===a.article.id?a.dragStyle:{}}})),r=o((()=>{const e=a.article.publishedScope===Ri.PERSONAL;return a.article.isOwner?`点击切换为${e?"公开":"个人"}可见`:(e?"个人":"公开")+"可见"})),s=()=>{const e=uu.resolve({name:"Article",params:{articleId:a.article.id}}),t=window.open(e.href,"_blank");null==t||t.focus()},c=e=>{l("startLongPress",e,a.article,e.currentTarget)},u=e=>{l("startLongPress",e,a.article,e.currentTarget)};return(e,t)=>(m(),d(g(Re),{class:y(["card-item cursor-pointer",n.value]),"data-article-id":e.article.id,onClick:P(s,["ctrl"]),"header-style":"padding-bottom:0.25rem;border-bottom: var(--border-1);",style:k(i.value)},{header:v((()=>[h("div",Er,[e.article.isOwner?(m(),d(g(le),{key:0},{trigger:v((()=>[h("div",{class:"scope-icon-wrapper clickable",onClick:t[0]||(t[0]=P((t=>e.$emit("toggleScope",e.article)),["stop"]))},[(m(),d(I(e.article.publishedScope==g(Ri).PERSONAL?g(nn):g(on)),{size:18}))])])),default:v((()=>[E(" "+w(r.value),1)])),_:1})):C("",!0),h("div",{class:"article-title",onClick:P(s,["stop"])},w(e.article.title),1)])])),"header-extra":v((()=>[p(g(se),{round:"",size:45,src:e.article.publisherAvatar,"object-fit":"cover",class:"article-avatar",onMousedown:P(c,["stop"]),onTouchstart:P(u,["stop"]),onMouseup:t[1]||(t[1]=P((t=>e.$emit("cancelLongPress")),["stop"])),onMouseleave:t[2]||(t[2]=P((t=>e.$emit("cancelLongPress")),["stop"])),onTouchcancel:t[3]||(t[3]=P((t=>e.$emit("cancelLongPress")),["stop"])),onContextmenu:t[4]||(t[4]=P((()=>{}),["prevent"]))},null,8,["src"])])),default:v((()=>[h("div",_r,[h("div",null,[(m(!0),f(L,null,S(e.article.tags,(e=>(m(),d(g(Me),{type:"primary",class:"card-tag",key:e},{default:v((()=>[E(w(e),1)])),_:2},1024)))),128))]),h("div",null,[p(g(wn),{size:24,class:"cursor-pointer",onClick:t[5]||(t[5]=P((t=>e.$emit("download",e.article.id)),["stop"]))})])]),h("div",Ir,[p(g(ae),{style:{"padding-right":"0.5rem"}},{default:v((()=>[p(Ti,{ref:t=>t&&e.$emit("setEditor",e.article.id,t),modelValue:e.article.contentObj,"onUpdate:modelValue":t[6]||(t[6]=t=>e.article.contentObj=t),editable:!1,"file-bucket":g(Vi),"all-extensions":!0,"character-limit":g(xi)},null,8,["modelValue","file-bucket","character-limit"])])),_:1})])])),_:1},8,["class","data-article-id","style"]))}}),[["__scopeId","data-v-6444c80f"]]),Pr={class:"trash-bin-text"},Br=Oa(i({__name:"TrashBin",props:{visible:{type:Boolean},isActive:{type:Boolean}},setup:e=>(e,t)=>(m(),d(V,{name:"trash-bin-fade"},{default:v((()=>[e.visible?(m(),f("div",{key:0,class:y(["trash-bin",{"trash-bin-active":e.isActive}])},[(m(),f("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"48",height:"48",style:k({color:e.isActive?"#ff4444":"#666666"})},t[0]||(t[0]=[h("path",{fill:"currentColor",d:"M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"},null,-1)]),4)),h("span",Pr,w(e.isActive?"释放删除":"拖拽到此处删除"),1)],2)):C("",!0)])),_:1}))}),[["__scopeId","data-v-4a042d79"]]);function $r(e,t,a){const l=e.cloneNode(!0);!function(e,t){const a=window.getComputedStyle(e);Ur(e,t,a);const l=e.querySelectorAll("*"),n=t.querySelectorAll("*");for(let i=0;i<l.length&&i<n.length;i++){const e=l[i];Ur(e,n[i],window.getComputedStyle(e))}}(e,l);const n=document.createElement("div");return n.style.cssText=`\n    position: fixed;\n    left: ${a.x}px;\n    top: ${a.y}px;\n    width: ${.5*t.width}px;\n    height: ${.5*t.height}px;\n    transform: translate(-50%, -50%) scale(1);\n    opacity: 0.8;\n    z-index: 9999;\n    pointer-events: none !important;\n    transition: none;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n    border-radius: 0.5rem;\n    overflow: hidden;\n    transform-origin: center center;\n  `,l.style.cssText=`\n    width: ${t.width}px;\n    height: ${t.height}px;\n    transform: scale(0.5);\n    transform-origin: top left;\n    margin: 0;\n    position: relative;\n    pointer-events: none !important;\n  `,function(e){e.style.pointerEvents="none !important",e.style.userSelect="none !important",e.style.setProperty("-webkit-user-select","none","important"),e.style.setProperty("-moz-user-select","none","important"),e.style.setProperty("-ms-user-select","none","important");const t=["onclick","onmousedown","onmouseup","ontouchstart","ontouchend","onscroll","onwheel"];t.forEach((t=>e.removeAttribute(t)));e.querySelectorAll("*").forEach((e=>{const a=e;a.style.pointerEvents="none !important",a.style.userSelect="none !important",a.style.setProperty("-webkit-user-select","none","important"),a.style.setProperty("-moz-user-select","none","important"),a.style.setProperty("-ms-user-select","none","important"),t.forEach((e=>a.removeAttribute(e)))}));e.querySelectorAll('.n-scrollbar, .article-content, [style*="overflow"]').forEach((e=>{const t=e;t.style.overflow="hidden !important",t.style.pointerEvents="none !important",t.classList.contains("article-content")&&(t.style.height="auto !important",t.style.maxHeight="none !important")}));e.querySelectorAll(".n-scrollbar-rail").forEach((e=>e.remove()));e.querySelectorAll(".n-scrollbar-content").forEach((e=>{const t=e;t.style.overflow="visible !important",t.style.height="auto !important",t.style.maxHeight="none !important",t.style.pointerEvents="none !important"}));e.querySelectorAll("iframe, script, noscript, object, embed").forEach((e=>e.remove()))}(l),n.appendChild(l),document.body.appendChild(n),ta.debug("DOM克隆元素已创建"),n}function Dr(e){e&&e.parentNode&&(e.parentNode.removeChild(e),ta.debug("克隆元素已清理"))}function Ur(e,t,a){let l="";["background","background-color","background-image","background-size","background-position","color","font-family","font-size","font-weight","font-style","border","border-radius","border-color","border-width","border-style","padding","margin","width","height","max-width","max-height","min-width","min-height","display","position","top","left","right","bottom","flex","flex-direction","flex-wrap","justify-content","align-items","text-align","text-decoration","text-transform","line-height","opacity","visibility","box-shadow","text-shadow","transform","transition"].forEach((e=>{const t=a.getPropertyValue(e);t&&"none"!==t&&"auto"!==t&&"initial"!==t&&(l+=`${e}: ${t} !important; `)})),l+="\n    pointer-events: none !important;\n    user-select: none !important;\n  ",t.classList.contains("article-content")?l+="\n      overflow: hidden !important;\n      height: auto !important;\n      max-height: none !important;\n    ":t.classList.contains("n-scrollbar-content")?l+="\n      overflow: visible !important;\n      height: auto !important;\n      max-height: none !important;\n    ":l+="\n      overflow: hidden !important;\n    ",l&&(t.style.cssText=(t.style.cssText||"")+l)}function Vr(e,t,a,l,n,i,o,r,s,c,u,d,m){const v=e=>{if(e instanceof MouseEvent)return{clientX:e.clientX,clientY:e.clientY};if(e instanceof TouchEvent){const t=e.touches.length>0?e.touches[0]:e.changedTouches[0];if(t)return{clientX:t.clientX,clientY:t.clientY}}return{clientX:0,clientY:0}},p=()=>{const e=document.querySelectorAll(".card-item:not(.dragging)");n.value=null,i.value=null,o.value=!1,e.forEach((e=>{var a,l;const s=e.getBoundingClientRect(),{x:c,y:u}=r.value;if(c>=s.left&&c<=s.right&&u>=s.top&&u<=s.bottom){const r=e.dataset.articleId;if(r&&r!==(null==(a=t.value)?void 0:a.id)){n.value=r;const t=(e=>{const t=document.querySelectorAll(".card-item:not(.dragging)"),a=e.getBoundingClientRect(),l=document.querySelector(".article-container");if((l?l.getBoundingClientRect().width:window.innerWidth)<=768)return!0;let n=0;return t.forEach((e=>{const t=e.getBoundingClientRect();!(t.bottom<=a.top||t.top>=a.bottom)&&n++})),1===n})(e);o.value=t,ta.debug("拖拽悬停检测:",{articleId:r,isOnlyCard:t,containerWidth:null==(l=document.querySelector(".article-container"))?void 0:l.getBoundingClientRect().width}),t?(i.value=u<s.top+s.height/2?"before":"after",ta.debug("垂直插入位置:",i.value)):(i.value=c<s.left+s.width/2?"before":"after",ta.debug("水平插入位置:",i.value))}}}))};let g=0;const h=()=>{var e;const a=Date.now();a-g>50&&(g=a,(null==(e=t.value)?void 0:e.isOwner)&&(()=>{const e=document.querySelector(".trash-bin");if(!e)return;const t=e.getBoundingClientRect(),{x:a,y:n}=r.value;l.value=a>=t.left&&a<=t.right&&n>=t.top&&n<=t.bottom})(),p())},f=t=>{if(!e.value)return;t.preventDefault();const{clientX:a,clientY:l}=v(t);r.value={x:a,y:l},c.value&&(c.value.style.left=`${a}px`,c.value.style.top=`${l}px`),h()},w=()=>{var a,o,r,v;e.value&&!s.value&&(s.value=!0,ta.debug("拖拽结束"),u.value&&(u.value(),u.value=null),l.value&&(null==(a=t.value)?void 0:a.isOwner)?(ta.debug("触发删除操作"),null==(o=m.onDelete)||o.call(m,t.value)):n.value&&i.value&&t.value&&(ta.debug("触发重新排序操作"),null==(r=m.onReorder)||r.call(m,t.value.id,n.value,i.value)),c.value&&(Dr(c.value),c.value=null),d(),null==(v=m.onDragEnd)||v.call(m),setTimeout((()=>{s.value=!1}),100))};return{startDragging:(l,n,i)=>{var o;e.value=!0,t.value=n,n.isOwner&&(a.value=!0,ta.debug("显示垃圾篓 - 文章拥有者"));const{clientX:s,clientY:d}=v(l);r.value={x:s,y:d};const p=i.closest(".card-item");if(p){const e=p.getBoundingClientRect();c.value=$r(p,e,r.value)}u.value=function(e){const{onMove:t,onEnd:a}=e,l=e=>{t(e)},n=()=>{a(),i()};document.addEventListener("mousemove",l,{passive:!1}),document.addEventListener("mouseup",n),document.addEventListener("touchmove",l,{passive:!1}),document.addEventListener("touchend",n),document.addEventListener("touchcancel",n),ta.debug("事件监听器已添加");const i=()=>{document.removeEventListener("mousemove",l),document.removeEventListener("mouseup",n),document.removeEventListener("touchmove",l),document.removeEventListener("touchend",n),document.removeEventListener("touchcancel",n),ta.debug("事件监听器已清理")};return i}({onMove:f,onEnd:w}),null==(o=m.onDragStart)||o.call(m,n)},handleDragMove:f,handleDragEnd:w}}function Fr(e={}){const{isDragging:t,draggedArticle:a,showTrashBin:l,isOverTrashBin:i,dragOverCardId:r,dragOverPosition:s,isSingleCardRow:c,dragPosition:u,dragStyle:d,isEnding:m,clonedElement:v,eventHandlers:p,forceReset:g,resetDragState:h}=function(){const e=n(!1),t=n(null),a=n(!1),l=n(!1),i=n(null),r=n(null),s=n(!1),c=n({x:0,y:0}),u=n(null),d=n(null),m=n(!1),v=o((()=>({}))),p=()=>{e.value=!1,t.value=null,a.value=!1,l.value=!1,i.value=null,r.value=null,s.value=!1};return z((()=>{u.value&&Dr(u.value),d.value&&d.value()})),{isDragging:e,draggedArticle:t,showTrashBin:a,isOverTrashBin:l,dragOverCardId:i,dragOverPosition:r,isSingleCardRow:s,dragPosition:c,dragStyle:v,isEnding:m,clonedElement:u,eventHandlers:d,forceReset:()=>{ta.debug("强制重置拖拽状态"),d.value&&(d.value(),d.value=null),u.value&&(Dr(u.value),u.value=null),p(),m.value=!1},resetDragState:p}}(),{isLongPressActive:f,startLongPress:w,cancelLongPress:y}=function(){let e=null,t={x:0,y:0};const a=n(!1),l=e=>{if(e instanceof MouseEvent)return{clientX:e.clientX,clientY:e.clientY};if(e instanceof TouchEvent){const t=e.touches.length>0?e.touches[0]:e.changedTouches[0];if(t)return{clientX:t.clientX,clientY:t.clientY}}return{clientX:0,clientY:0}},i=()=>{e&&(clearTimeout(e),e=null),a.value=!1};return{isLongPressActive:a,startLongPress:(n,o,r,s)=>{ta.debug("开始长按检测:",o.title),n.preventDefault(),n.stopPropagation(),i();const{clientX:c,clientY:u}=l(n);t={x:c,y:u},a.value=!0;const d=e=>{if(!a.value)return;const{clientX:n,clientY:o}=l(e);Math.sqrt(Math.pow(n-t.x,2)+Math.pow(o-t.y,2))>10&&(ta.debug("移动距离超过阈值，取消长按"),i(),v())},m=()=>{i(),v()},v=()=>{a.value=!1,document.removeEventListener("mousemove",d),document.removeEventListener("touchmove",d),document.removeEventListener("mouseup",m),document.removeEventListener("touchend",m),document.removeEventListener("touchcancel",m)};document.addEventListener("mousemove",d,{passive:!1}),document.addEventListener("touchmove",d,{passive:!1}),document.addEventListener("mouseup",m),document.addEventListener("touchend",m),document.addEventListener("touchcancel",m),e=setTimeout((()=>{a.value&&(ta.debug("长按触发，开始拖拽:",o.title),v(),s(n,o,r))}),500)},cancelLongPress:()=>{ta.debug("取消长按"),a.value&&(ta.debug("取消长按检测"),i())},cleanup:i}}(),{startDragging:b}=Vr(t,a,l,i,r,s,c,u,m,v,p,h,e);return{isDragging:t,draggedArticle:a,showTrashBin:l,isOverTrashBin:i,dragPosition:u,dragStyle:d,dragOverCardId:r,dragOverPosition:s,isSingleCardRow:c,isLongPressActive:f,startLongPress:(e,t,a)=>{w(e,t,a,b)},cancelLongPress:y,forceReset:g}}function Or(e,t,a,l,n,i){const o=(o=!1,r,s)=>{ta.debug("loadArticles called:",{loadMore:o,loading:a.value,noMore:l.value,searchCondition:e,forceLoadSize:s});const u=void 0!==s?s:i.value;if(a.value||l.value)return ta.debug("loadArticles early return:",{loading:a.value,noMore:l.value}),Promise.resolve();a.value=!0;const d=t.value.length>0?t.value[t.value.length-1]:null,m=null==d?void 0:d.id;return new Promise(((i,o)=>{c((()=>{const s={...e.value||{},id:m,loadSize:u};ta.debug("Making API request with params:",s),Yn.search(s,r).then((e=>{if(ta.debug("API response received:",e),!e||!e.data)return void i();const a=e.data;if(0===a.length)return l.value=!0,void i();a.length<u&&(l.value=!0);if(!(e=>{const a=new Set(t.value.map((e=>e.id))),l=e.filter((e=>!a.has(e.id)));if(0===l.length)return!1;const n=l.map((e=>{var t,a;return{...e,contentObj:qn.toJsonObject(e.content),publisherAvatar:(a=e.publisherAvatar,_a.getResourceURL(a)),tags:(null==(t=e.tag)?void 0:t.split(","))||[]}}));return t.value=[...t.value,...n],!0})(a))return l.value=!0,void i();n.value+=a.length})).catch((e=>{"CanceledError"!==e.name&&"canceled"!==e.message?(ta.error("加载文章失败:",e),l.value=!1,(null==r?void 0:r.aborted)?i():o(e)):i()})).finally((()=>{(null==r?void 0:r.aborted)||(a.value=!1)}))}))}))};return{resetList:()=>{t.value=[],l.value=!1,n.value=0,o()},loadArticles:o}}function Hr(e){const t=function(){const e=n([]),t=n(!1),a=n(!1),l=n(6),i=n(new Map),o=n(0),r=n(1);return{articleList:e,loading:t,noMore:a,cardColSpan:l,articleTiptapEditorMap:i,containerRef:n(null),scrollContainerRef:n(null),currentLoadedArticlesCount:o,cardsPerRow:r}}(),{articleList:a,loading:l,noMore:i,cardColSpan:r,containerRef:c,currentLoadedArticlesCount:u,cardsPerRow:d}=t,m=function(e,t,a,l,n,i){const r=["#ffd6d6","#ffe8d1","#fff8c4","#d5edd7","#d0e8fa","#ded6f2","#ebcfe9","#f8d4de"],c=["#8c3a3a","#7d6339","#75763a","#366d5a","#355678","#534878","#664766","#6a4251"],u=o((()=>da.value===aa.DARK)),d=o((()=>{var e,i;const o=t.value,r=(null==(e=a.value)?void 0:e.clientHeight)||0;let s;if(0===l.value)s=Math.ceil(r/470)*o,s=Math.max(o,s);else{const e=Math.ceil(((null==(i=a.value)?void 0:i.clientHeight)||0)/470)*o;o<=2?s=2*o:(s=Math.ceil(e/2),s=Math.ceil(s/o)*o),s=Math.max(o,s)}return ta.debug("Calculated Load Size:",s,"Cards Per Row:",o,"Current Loaded Articles:",n.value,"Card Height:",470),s}));return s(t,((e,t)=>{if(e!==t){const a=l.value%e;if(0!==a){const l=e-a;ta.debug(`视口变化，每行卡片数从 ${t} 变为 ${e}，需要加载 ${l} 篇文章以补齐倍数。`),i(!0,void 0,l)}}})),{getCardColor:(e,t)=>{const a=u.value?c:r;return a[t%a.length]},calculatedLoadSize:d,updateColSpan:()=>{const a=window.innerWidth;let l=24,n=1;a>=1680?(l=6,n=4):a>=1260?(l=8,n=3):a>=840?(l=12,n=2):(l=24,n=1),e.value=l,t.value=n}}}(r,d,c,o((()=>a.value.length)),u,Or(e.searchCondition,a,l,i,u,o((()=>0))).loadArticles),v=Or(e.searchCondition,a,l,i,u,m.calculatedLoadSize),p=function(e){const t=Ae();return{handleToggleScope:e=>{const a=e.publishedScope===Ri.PERSONAL?Ri.PUBLIC:Ri.PERSONAL;t.warning(Bi({title:"切换发布范围",content:`确定要将文章《${e.title}》切换为${Ai[a]}可见吗？`,positiveText:"确定",negativeText:"取消",onPositiveClick:()=>{Yn.togglePublishedScope(e.id).then((t=>{200===t.code?(e.publishedScope=a,xa.success(`文章已切换为${Ai[a]}可见`)):xa.error(t.message||"操作失败")}))}}))},handleDeleteArticle:a=>{t.warning(Bi({title:"删除文章",content:`确定要删除文章《${a.title}》吗？此操作不可恢复。`,positiveText:"删了",negativeText:"算了",onPositiveClick:()=>{Yn.delete(a.id).then((t=>{if(200===t.code){const t=e.value.findIndex((e=>e.id===a.id));t>-1&&e.value.splice(t,1),xa.success("文章已删除")}else xa.error(t.message||"删除失败")})).catch((()=>{xa.error("删除失败，请稍后再试")}))}}))},handleReorderArticles:(t,a,l)=>{const n=e.value.findIndex((e=>e.id===t)),i=e.value.findIndex((e=>e.id===a));if(-1===n||-1===i)return;const[o]=e.value.splice(n,1);let r=i;r="after"===l?n<i?i:i+1:i,e.value.splice(r,0,o),xa.success("移动成功！")}}}(a);return{...t,...m,...v,...p}}const jr={class:"infinite-load-info"},Nr=Oa(i({__name:"ArticleList",props:{searchCondition:{}},emits:["reset"],setup(e,{expose:t,emit:a}){const l=e,{articleList:n,loading:i,noMore:o,cardColSpan:c,articleTiptapEditorMap:u,containerRef:w,scrollContainerRef:y,getCardColor:b,updateColSpan:k,resetList:x,loadArticles:T,handleToggleScope:M,handleDeleteArticle:R,handleReorderArticles:A}=Hr(l),{isDragging:E,draggedArticle:_,showTrashBin:I,isOverTrashBin:z,dragStyle:P,dragOverCardId:B,dragOverPosition:$,isSingleCardRow:D,startLongPress:U,cancelLongPress:V,isLongPressActive:F}=Fr({onDragStart:e=>{ta.debug("开始拖拽文章:",e.title)},onDelete:R,onReorder:A});s(l.searchCondition,((e,t)=>{ta.debug("搜索条件变化:",{oldCondition:t,newCondition:e,articleListLength:n.value.length});Object.keys(e).some((a=>(["searchKey","tag"].includes(a)||!!["owner","interaction","favorite"].includes(a))&&e[a]!==t[a]))?(ta.info("搜索条件发生实质性变化，重置文章列表"),x(),T()):ta.debug("搜索条件未发生实质性变化，不执行重置")}),{deep:!0}),r((()=>{k(),window.addEventListener("resize",O)}));const O=()=>{k()},H=()=>{T(!0)},j=(e,t,a)=>{U(e,t,a)},N=e=>{const t=u.value.get(e);(null==t?void 0:t.editor)&&Yn.md(e,t.editor)},q=(e,t)=>{u.value.set(e,t)};t({loadArticles:T,resetList:x});const Y=()=>{},W=()=>{},J=()=>{F&&(ta.debug("容器触摸取消 - 取消长按检测"),V())};return(e,t)=>(m(),f("div",{class:"article-container",ref_key:"containerRef",ref:w,onTouchstart:Y,onTouchend:W,onTouchcancel:J},[p(g(Ce),{onLoad:H,distance:100,class:"infinite-scroll-container",ref_key:"scrollContainerRef",ref:y},{default:v((()=>[p(g(Ee),{gutter:20,style:{width:"100%","box-sizing":"border-box",margin:"0 auto",padding:"0 0.25rem",flex:"1","overflow-y":"auto"}},{default:v((()=>[(m(!0),f(L,null,S(g(n),((e,t)=>(m(),d(g(_e),{key:e.id,span:g(c)},{default:v((()=>[p(zr,{article:e,index:t,"card-color":g(b)(e.id,t),"is-dragging":g(E),"dragged-article":g(_)||void 0,"drag-over-card-id":g(B)||void 0,"drag-over-position":g($)||void 0,"is-single-card-row":g(D),"drag-style":g(P),onToggleScope:g(M),onStartLongPress:j,onCancelLongPress:g(V),onDownload:N,onSetEditor:q},null,8,["article","index","card-color","is-dragging","dragged-article","drag-over-card-id","drag-over-position","is-single-card-row","drag-style","onToggleScope","onCancelLongPress"])])),_:2},1032,["span"])))),128))])),_:1}),h("div",jr,[g(i)?(m(),d(g(Le),{key:0,class:"display-flex"})):C("",!0),g(o)?(m(),d(g(Se),{key:1,description:"没有更多文章了..."})):C("",!0)])])),_:1},512),p(Br,{visible:g(I),"is-active":g(z)},null,8,["visible","is-active"])],544))}}),[["__scopeId","data-v-f5916f28"]]);const qr=i({__name:"Danmaku",props:{danmus:{type:Array,required:!0,default:()=>[]},channels:{type:Number,default:0},autoplay:{type:Boolean,default:!0},loop:{type:Boolean,default:!1},useSlot:{type:Boolean,default:!1},debounce:{type:Number,default:100},speeds:{type:Number,default:200},randomChannel:{type:Boolean,default:!1},fontSize:{type:Number,default:18},top:{type:Number,default:4},right:{type:Number,default:0},isSuspend:{type:Boolean,default:!1},extraStyle:{type:String,default:""}},emits:["list-end","play-end","dm-over","dm-out","update:danmus"],setup(e,{expose:t,emit:a}){const l=e,i=a,s=n(document.createElement("div")),c=n(document.createElement("div")),u=n(0),d=n(0),v=n(0),p=n(0),g=n(48),w=n(0),b=n(!1),k=n(!1),x=A({});const C=function(e,t,a="modelValue"){return o({get:()=>e[a],set:e=>{t(`update:${a}`,e)}})}(l,i,"danmus"),L=A({channels:o((()=>l.channels||p.value)),autoplay:o((()=>l.autoplay)),loop:o((()=>l.loop)),useSlot:o((()=>l.useSlot)),debounce:o((()=>l.debounce)),randomChannel:o((()=>l.randomChannel))}),S=A({height:o((()=>g.value)),fontSize:o((()=>l.fontSize)),speeds:o((()=>{const e=l.speeds,t=u.value;if(t<320)return.25*e;if(t<960)return.5*e;if(t>=1920)return e;return e*(.5+(t-960)/960*.5)})),top:o((()=>l.top)),right:o((()=>l.right))}),{initCore:T,resize:E}=function(e,t,a,l,n){function i(){if(a.value=e.value.offsetWidth,l.value=e.value.offsetHeight,0===a.value||0===l.value)throw new Error("获取不到容器宽高")}return{initCore:i,resize:function(){i();const e=t.value.getElementsByClassName("dm");for(let t=0;t<e.length;t++){const l=e[t],i=null!==l.querySelector("img");let o=l.offsetWidth;i&&(o+=100);const r=a.value+o;l.style.setProperty("--dm-scroll-width",`-${r}px`),l.style.left=`${a.value}px`,l.style.animationDuration=r/n.speeds+"s"}}}}(s,c,u,d,S),{draw:I,insert:z,add:P,push:B}=function(e,t,a,l,n,i,o,r,s,c,u,d,m){function v(i){try{if(i&&i.content){let e=i.content;if("string"==typeof e)try{e=JSON.parse(e)}catch(v){ta.warn("弹幕JSON解析失败，使用原始文本:",v)}f({id:i.commentId||Date.now().toString(),content:e})}const h=r.loop?t.value%e.value.length:t.value,w=i||e.value[h];let y=document.createElement("div");r.useSlot&&m?y=p(w,h).$el:(y.innerHTML=w,y.setAttribute("style",d),y.style.fontSize=`${o.fontSize}px`,y.style.lineHeight="3rem"),y.classList.add("dm"),n.value.appendChild(y),y.style.opacity="0";const b=y.offsetHeight;let k=y.offsetWidth;const x=null!==y.querySelector("img");x&&(k+=150),c.value||(c.value=48),r.channels||(s.value=Math.floor(l.value/(o.height+o.top)));let C=g(y);if(C>=0){const i=o.height,s=()=>{C*(i+o.top)+b>=l.value&&(C--,s())};s(),ta.debug("danmaku height top: ",i,o.top),y.classList.add("move"),y.dataset.index=`${h}`,y.dataset.channel=C.toString(),y.style.opacity="1";const c=C*(i+o.top)+"px";y.style.top=c,y.style.left=`${a.value}px`;const d=()=>{const e=x?Math.max(y.offsetWidth+100,k):y.offsetWidth,t=a.value+e,l=t/o.speeds;y.style.animationDuration=`${l}s`,y.style.setProperty("--dm-scroll-width",`-${t}px`)};if(x?requestAnimationFrame((()=>{requestAnimationFrame(d)})):d(),y.addEventListener("animationend",(()=>{Number(y.dataset.index)!==e.value.length-1||r.loop||u("play-end",y.dataset.index),n.value&&n.value.removeChild(y)})),t.value++,x){const e=y.querySelectorAll("img");let t=0;const l=e.length,n=()=>{if(t++,t===l){const e=y.offsetWidth+50,t=a.value+e,l=t/o.speeds;y.style.animationDuration=`${l}s`,y.style.setProperty("--dm-scroll-width",`-${t}px`)}};e.forEach((e=>{e.complete?n():(e.addEventListener("load",n,{once:!0}),e.addEventListener("error",n,{once:!0}))}))}}else n.value.removeChild(y)}catch(h){ta.error("添加弹幕时发生错误:",h)}}function p(e,t){return H({render:()=>R("div",{},[m&&m({danmu:e,index:t})])}).mount(document.createElement("div"))}function g(e){let t=[...Array(r.channels).keys()];r.randomChannel&&(t=t.sort((()=>.5-Math.random())));for(const a of t){const t=i[a];if(!t||!t.length)return i[a]=[e],e.addEventListener("animationend",(()=>i[a].splice(0,1))),a%r.channels;for(let l=0;l<t.length;l++){const n=h(t[l])-10;if(n<=.75*(e.offsetWidth-t[l].offsetWidth)||n<=0)break;if(l===t.length-1)return i[a].push(e),e.addEventListener("animationend",(()=>i[a].splice(0,1))),a%r.channels}}return-1}function h(e){const t=e.offsetWidth||parseInt(e.style.width),a=e.getBoundingClientRect().right||n.value.getBoundingClientRect().right+t;return n.value.getBoundingClientRect().right-a}function f(a){if(t.value===e.value.length)return e.value.push(a),e.value.length-1;{const l=t.value%e.value.length;return e.value.splice(l,0,a),l+1}}return{draw:function(){if(e.value.length)if(t.value>e.value.length-1){const e=n.value.children.length;r.loop&&(e<t.value&&(u("list-end"),t.value=0),v())}else v()},insert:v,add:f,push:function(t){return e.value.push(t),e.value.length-1},getChannelIndex:g,getDanRight:h,getSlotComponent:p}}(C,w,u,d,c,x,S,L,p,g,i,l.extraStyle,j().dm),{play:$,clear:D,stop:U,pause:V,show:F,hide:O,getPlayState:N}=function(e,t,a,l,n,i,o,r,s){function c(){clearInterval(o.value),o.value=0}function u(){c(),a.value=0}return{play:function(){if(l.value=!1,!o.value){const e=s instanceof Object?s.value:s;o.value=window.setInterval((()=>r()),e)}},clearTimer:c,clear:u,stop:function(){Object.assign(i,{}),e.value.innerHTML="",l.value=!0,n.value=!1,u()},pause:function(){l.value=!0},show:function(){n.value=!1},hide:function(){n.value=!0},getPlayState:function(){return!l.value}}}(c,0,w,k,b,x,v,I,L.debounce),{initSuspendEvents:q}=function(e,t){return{initSuspendEvents:function(){let a=[];e.value.addEventListener("mouseover",(e=>{let l=e.target;l.className.includes("dm")||(l=l.closest(".dm")||l),l.className.includes("dm")&&(a.includes(l)||(t("dm-over",{el:l}),l.classList.add("pause"),a.push(l)))})),e.value.addEventListener("mouseout",(e=>{let l=e.target;l.className.includes("dm")||(l=l.closest(".dm")||l),l.className.includes("dm")&&(t("dm-out",{el:l}),l.classList.remove("pause"),a.forEach((e=>{e.classList.remove("pause")})),a=[])}))}}}(c,i);function Y(){T(),l.isSuspend&&q(),L.autoplay&&$()}return r((()=>{Y()})),M((()=>{D()})),t({container:s,dmContainer:c,hidden:b,paused:k,danmuList:C,getPlayState:N,resize:E,play:$,pause:V,stop:U,show:F,hide:O,reset:function(){g.value=0,Y()},add:P,push:B,insert:z}),(e,t)=>(m(),f("div",{ref_key:"container",ref:s,class:"vue-danmaku"},[h("div",{ref_key:"dmContainer",ref:c,class:y(["danmus",{show:!b.value},{paused:k.value}])},null,2),_(e.$slots,"default")],512))}}),Yr=500,Wr=.5,Jr=1,Kr={minScale:.5,maxScale:3};function Xr(e,t,a,l){a("image-preview-open");const{modal:n,previewImg:i}=function(){const e=document.createElement("div");e.classList.add("modal-overlay");const t=document.createElement("img");return t.alt="图片预览",e.appendChild(t),document.body.appendChild(e),e.classList.add("modal-overlay-active"),{modal:e,previewImg:t}}(),o=function(e,t){return{isOriginal:"true"===e.dataset.isOriginal,thumbnailSrc:e.dataset.thumbnailSrc||t,isExternal:t.startsWith("http://")||t.startsWith("https://")}}(e,t);o.isExternal||!o.thumbnailSrc.includes(Ia)||o.isOriginal?(i.src=e.src,i.style.opacity=String(Jr)):function(e,t,a,l){e.src=l.src,e.style.opacity=String(Wr);const n=document.createElement("div");n.classList.add("loading-spinner"),t.appendChild(n);const i=new Image,o=Date.now();i.onload=()=>{const t=Date.now()-o,a=()=>{n.style.display="none",e.src=i.src,e.style.opacity=String(Jr),e.dataset.originalFullUrl=i.src};t<Yr?setTimeout(a,Yr-t):a()},i.onerror=()=>{n.style.display="none",e.style.opacity=String(Jr)},i.src=_a.getResourceURL(a.replace(Ia,""))}(i,n,t,e);const r=cl(i,Kr,(()=>s()),n),s=function(e,t,a,l,n,i,o,r){return()=>{e.classList.remove("modal-overlay-active"),e.addEventListener("transitionend",(()=>{e.classList.contains("modal-overlay-active")||(!n.isExternal&&t.dataset.originalFullUrl&&n.thumbnailSrc.includes(Ia)&&!n.isOriginal&&(a.src=t.dataset.originalFullUrl,a.dataset.isOriginal="true",a.dataset.originalSrc=l.replace(Ia,"")),document.body.removeChild(e),o(),r.cleanup(),i("image-preview-close"))}),{once:!0})}}(n,i,e,t,o,a,il(n,(()=>s()),r.handleWheelZoom),r);r.initialize()}function Gr(e){var t,a,l,n;if(!e)return"";let i="";try{if("text"===e.type&&e.text){let l=e.text;if(e.marks&&e.marks.length>0)for(const n of e.marks)"bold"===n.type?l=`<strong>${l}</strong>`:"italic"===n.type?l=`<em>${l}</em>`:"underline"===n.type?l=`<u>${l}</u>`:"strike"===n.type?l=`<s>${l}</s>`:"code"===n.type?l=`<code>${l}</code>`:"link"===n.type&&(null==(t=n.attrs)?void 0:t.href)?l=`<a href="${n.attrs.href}" target="_blank">${l}</a>`:"textStyle"===n.type&&(null==(a=n.attrs)?void 0:a.color)&&(l=`<span style="color: ${n.attrs.color}">${l}</span>`);i+=l}else if("mention"===e.type&&(null==(l=e.attrs)?void 0:l.label))try{const t=e.attrs.avatar?_a.getResourceURL(e.attrs.avatar):"",a=['data-type="mention"',`data-id="${e.attrs.id||""}"`,`data-label="${e.attrs.label}"`,`data-avatar="${e.attrs.avatar||""}"`].join(" "),l=`<span class="mention-name">@${e.attrs.label}</span>`;i+=`<span class="mention" ${a} contenteditable="false">${l}${t?`<img src="${t}" alt="${e.attrs.label}" class="mention-avatar" loading="eager" decoding="async" referrerpolicy="no-referrer" onerror="this.style.display='none';" />`:""}</span>`}catch(o){i+=`<span class="mention" data-type="mention" data-label="${e.attrs.label}"><span class="mention-name">@${e.attrs.label}</span></span>`}else if("image"===e.type&&(null==(n=e.attrs)?void 0:n.src))try{const t=e.attrs.src;if(t.startsWith("http://")||t.startsWith("https://"))i+=`<img src="${t}" class="danmaku-image"\n                     loading="eager" decoding="async" alt="图片" referrerpolicy="no-referrer"\n                     data-original-src="${t}"\n                     data-is-original="true"\n                     onerror="this.replaceWith(document.createTextNode('[图片]'));" />`;else{let e,a;if(t.includes(Ia))e=_a.getResourceURL(t),a=t;else{const l=t.split("/"),n=l.pop();a=`${l.join("/")}${Ia}/${n}`,e=_a.getResourceURL(a)}const l=_a.getResourceURL(t.replace(Ia,""));i+=`<img src="${e}" class="danmaku-image"\n                     loading="eager" decoding="async" alt="图片" referrerpolicy="no-referrer"\n                     data-original-src="${t}"\n                     data-thumbnail-src="${a}"\n                     data-is-original="false"\n                     onerror="if (!this.dataset.tried) { this.dataset.tried = 'true'; this.src = '${l}'; this.dataset.isOriginal = 'true'; }\n                     else { this.replaceWith(document.createTextNode('[图片]')); }" />`}}catch(o){i+='<span class="image-placeholder">[图片]</span>'}else if("hardBreak"===e.type)i+=" ";else if(e.type&&["paragraph","heading","blockquote"].includes(e.type)){if(e.content&&Array.isArray(e.content)){i+=e.content.map((e=>Gr(e))).join("")+" "}}else e.content&&Array.isArray(e.content)&&(i+=e.content.map((e=>Gr(e))).join(""))}catch(o){if("text"===e.type&&e.text)return e.text;if("image"===e.type)return"[图片]"}return i}function Zr(e){try{return e&&"object"==typeof e?e.content&&Array.isArray(e.content)&&0!==e.content.length?Gr(e):"<span>[空内容]</span>":"<span>[无法显示内容]</span>"}catch(t){return"<span>[渲染失败]</span>"}}const Qr=["innerHTML"],es=i({__name:"DanmakuRenderer",props:{content:{type:Object,required:!0}},emits:["image-preview-open","image-preview-close"],setup(e,{emit:t}){const a=e,l=t,{content:i}=N(a),s=n(null),c=o((()=>Zr(i.value)));return r((()=>{const e=e=>{const t=e.target;if("IMG"===t.tagName&&t.classList.contains("danmaku-image")){const a=t,n=a.dataset.originalSrc;n&&(e.stopPropagation(),e.preventDefault(),Xr(a,n,l))}};s.value&&(s.value.addEventListener("click",e),s.value._danmakuImageClickHandler=e)})),z((()=>{s.value&&s.value._danmakuImageClickHandler&&(s.value.removeEventListener("click",s.value._danmakuImageClickHandler),delete s.value._danmakuImageClickHandler)})),(e,t)=>(m(),f("div",{class:"danmaku-renderer",ref_key:"rendererRef",ref:s,innerHTML:c.value},null,8,Qr))}}),ts="/topic/comments";const as={class:"comment-container"},ls={key:0,class:"danmaku-empty-hint"},ns=["onDblclick"],is={class:"comment-danmaku-publisher"},os={class:"comment-danmaku-content"},rs=Oa(i({__name:"CommentDanmaku",props:{searchCondition:{type:Object,required:!0},loop:{type:Boolean,default:!1},pause:{type:Boolean,default:!1}},emits:["search","update:loop","update:pause"],setup(e,{expose:t,emit:a}){const l=e,{commentList:i,danmakuRef:o,danmakuConfig:c,isSubscribed:u}=function(e,t){const a=n([]),l=n(),i=A({isSuspend:!0,useSlot:!0,speeds:160,debounce:200,top:10,right:0,channels:0,randomChannel:!0,fontSize:14,loop:!1,pause:!1,autoplay:!1}),o=n(!1);return s((()=>e.value),(e=>{i.loop=e})),s((()=>t.value),(e=>{i.pause=e,l.value&&(e?l.value.pause():l.value.play())})),{commentList:a,danmakuRef:l,danmakuConfig:i,isSubscribed:o}}(q(l,"loop"),q(l,"pause")),{clearDanmaku:d,addCommentList:y,handleDanmuClick:k,subscribeComment:x,unsubscribeComment:L,handleImagePreviewOpen:S,handleImagePreviewClose:T,resize:M,play:R,pause:E}=function(e,t,a,l){const n=e=>_a.getResourceURL(e),i=a=>{const l=na.parse(a);l&&ba("comment-received:"+l.commentId,(()=>{if(!e.value.some((e=>e.id===l.commentId)))if(l.commentId&&l.articleId&&l.publisher)try{const e={id:l.commentId,articleId:l.articleId,publisher:l.publisher,publisherAvatar:n(l.publisherAvatar||""),content:l.content||"",contentObj:l.content?qn.toJsonObject(l.content):{type:"doc",content:[{type:"paragraph",content:[]}]}};t.value&&t.value.insert(e)}catch(a){ta.error("处理弹幕消息时出错:",a)}else ta.warn("消息缺少必要字段:",l)}))};return{clearDanmaku:()=>{e.value=[],t.value&&t.value.reset()},addCommentList:a=>{if(ta.debug("addCommentList 被调用:",{inputList:a,inputLength:(null==a?void 0:a.length)||0,currentListLength:e.value.length}),!a||!Array.isArray(a))return void ta.warn("addCommentList 收到无效的评论列表:",a);const l=new Set(e.value.map((e=>e.id))),i=a.filter((e=>!l.has(e.id)));if(ta.debug("过滤后的新评论:",{newCommentsLength:i.length,existingIds:Array.from(l)}),0!==i.length)try{const a=i.map((e=>({...e,publisherAvatar:n(e.publisherAvatar||""),content:e.content||"",contentObj:e.content?qn.toJsonObject(e.content):{type:"doc",content:[{type:"paragraph",content:[]}]}}))),l=10;for(let t=0;t<a.length;t+=l){const n=a.slice(t,t+l);setTimeout((()=>{e.value.push(...n)}),50*t)}e.value.length>200&&(e.value=e.value.slice(-200)),t.value&&t.value.play()}catch(o){ta.error("处理评论列表时出错:",o)}else ta.debug("没有新评论需要添加")},handleDanmuClick:e=>{const t=uu.resolve({name:"Article",params:{articleId:e.articleId,commentId:e.id}}),a=window.open(t.href,"_blank");a&&a.focus()},subscribeComment:()=>{l.value||(go.subscribe(ts,i),l.value=!0)},unsubscribeComment:()=>{l.value&&(go.unsubscribe(ts),l.value=!1)},handleImagePreviewOpen:()=>{t.value&&t.value.pause()},handleImagePreviewClose:()=>{t.value&&!a.pause&&t.value.play()},resize:()=>{t.value&&t.value.resize()},play:()=>{t.value&&t.value.play()},pause:()=>{t.value&&t.value.pause()}}}(i,o,c,u);return t({get danmakuLoop(){return c.loop},set danmakuLoop(e){c.loop=e},clearDanmaku:d,addCommentList:y,subscribeComment:x,unsubscribeComment:L,resize:M,play:R,pause:E}),r((()=>{x()})),z((()=>{L()})),(e,t)=>(m(),f("div",as,[0===g(i).length?(m(),f("div",ls,t[1]||(t[1]=[b('<div class="hint-content" data-v-31debac7><h3 data-v-31debac7>🎯 弹幕搜索</h3><p data-v-31debac7>在搜索框中输入关键词，或选择筛选条件来查看相关评论弹幕：</p><ul data-v-31debac7><li data-v-31debac7><strong data-v-31debac7>我的</strong> - 查看我发布的评论</li><li data-v-31debac7><strong data-v-31debac7>互动</strong> - 查看我互动过的评论</li><li data-v-31debac7><strong data-v-31debac7>收藏</strong> - 查看我收藏的评论</li></ul><p class="hint-note" data-v-31debac7>💡 也可以点击标签来筛选特定主题的评论</p></div>',1)]))):C("",!0),p(qr,Y({ref_key:"danmakuRef",ref:o,class:"comment-danmaku",danmus:g(i),"onUpdate:danmus":t[0]||(t[0]=e=>B(i)?i.value=e:null)},g(c)),{dm:v((({danmu:e})=>[h("span",{class:"comment-danmaku-item cursor-pointer",onDblclick:t=>g(k)(e)},[h("span",is,[p(g(se),{round:"",size:28,src:e.publisherAvatar,"object-fit":"cover",lazy:!0},null,8,["src"]),h("span",null,w(e.publisher)+": ",1)]),h("span",os,[p(es,{content:e.contentObj,onImagePreviewOpen:g(S),onImagePreviewClose:g(T)},null,8,["content","onImagePreviewOpen","onImagePreviewClose"])])],40,ns)])),_:1},16,["danmus"])]))}}),[["__scopeId","data-v-31debac7"]]),ss=Oa(i({__name:"CreateButton",emits:["click","long-press"],setup(e,{emit:t}){const a=t,l=n(!1);n(!1);const i=n(null),o=n(null),s=n(!1),c=n(null),u=()=>{s.value||a("click")},v=()=>{w()},p=()=>{b()},h=e=>{e.preventDefault(),w()},f=e=>{e.preventDefault(),b()},w=()=>{s.value=!1,c.value=window.setTimeout((()=>{s.value=!0,a("long-press")}),1e3)},b=()=>{c.value&&(clearTimeout(c.value),c.value=null),setTimeout((()=>{s.value=!1}),100)},k=()=>{l.value||(o.value&&(clearTimeout(o.value),o.value=null),l.value=!0,i.value=window.setTimeout((()=>{l.value=!1,x()}),1500))},x=()=>{o.value&&(clearTimeout(o.value),o.value=null);const e=5e3+1e4*Math.random();o.value=window.setTimeout((()=>{C()}),e)},C=()=>{l.value?x():(l.value=!0,i.value=window.setTimeout((()=>{l.value=!1,x()}),1500))};return r((()=>{x()})),z((()=>{i.value&&(clearTimeout(i.value),i.value=null),o.value&&(clearTimeout(o.value),o.value=null),c.value&&(clearTimeout(c.value),c.value=null)})),(e,t)=>(m(),d(g(gn),{size:36,color:"var(--blue)",onClick:u,onMousedown:v,onMouseup:p,onMouseleave:p,onTouchstart:h,onTouchend:f,class:y(["cursor-pointer create-button",{"is-rotating":l.value,"is-long-pressing":s.value}]),ref:"createButtonRef",onMouseenter:k},null,8,["class"]))}}),[["__scopeId","data-v-6d2d7876"]]),cs={class:"search-container"},us=Oa(i({__name:"SearchBar",props:{placeholder:{type:String,default:"感兴趣的内容"},modelValue:{type:Object,required:!0}},emits:["update:modelValue","search"],setup(e,{emit:t}){const a=e,l=t,i=n({...a.modelValue});s((()=>a.modelValue),(e=>{i.value={...e},u()}),{deep:!0});const o=()=>{l("update:modelValue",{...i.value})},r=n(""),c=[{name:"owner",label:"我的"},{name:"interaction",label:"互动"},{name:"favorite",label:"收藏"}],u=()=>{r.value="";for(const e of c)if(i.value[e.name]){r.value=e.name;break}};u();const d=()=>{o(),h(),l("search")},h=()=>{ia.set(To,i.value)};return(t,a)=>(m(),f("div",cs,[p(g(oe),{style:{"border-radius":"0.5rem"},value:i.value.searchKey,"onUpdate:value":a[0]||(a[0]=e=>i.value.searchKey=e),size:"large",placeholder:e.placeholder,clearable:"",onInput:d},{suffix:v((()=>[p(g(pn),{onClick:d,class:"cursor-pointer",size:20})])),_:1},8,["value","placeholder"]),p(g(Ie),{value:r.value,"justify-content":"space-evenly"},{default:v((()=>[(m(),f(L,null,S(c,((e,t)=>p(g(ze),{key:t,name:e.name,label:e.label,onClick:t=>{return a=e.name,r.value===a?r.value="":r.value=a,i.value[a]=!i.value[a],Object.keys(i.value).forEach((e=>{e!==a&&"searchKey"!==e&&"tag"!==e&&(i.value[e]=!1)})),o(),h(),void l("search");var a}},null,8,["name","label","onClick"]))),64))])),_:1},8,["value"])]))}}),[["__scopeId","data-v-070d2ed0"]]),ds={key:0,class:"tag-bar-container"},ms=Oa(i({__name:"TagBar",props:{modelValue:{type:String,default:""}},emits:["update:modelValue","tagSelected"],setup(e,{expose:t,emit:a}){const l=e,i=a,c=n([]),u=n(l.modelValue),p=n([]),h=o((()=>c.value.find((e=>e.name===u.value))));s((()=>l.modelValue),(e=>{ta.debug("外部标签值变化:",{oldValue:u.value,newValue:e,hotTags:c.value}),u.value=e}));return r((()=>{(async()=>{try{const e=await Yn.getHotTags(10);e.data&&(c.value=e.data,ta.debug("热门标签加载成功:",c.value))}catch(e){ta.error("加载热门标签失败:",e)}})()})),t({tagSelectionHistory:p,currentTagInfo:h,hotTags:c}),(e,t)=>c.value.length>0?(m(),f("div",ds,[(m(!0),f(L,null,S(c.value,(e=>(m(),d(g(Me),{key:e.name,type:u.value===e.name?"primary":"default",class:"hot-tag",bordered:!1,onClick:t=>(e=>{const t=u.value;u.value===e?u.value="":u.value=e,p.value.push({timestamp:Date.now(),from:t,to:u.value}),p.value.length>10&&p.value.shift(),ta.debug("标签选择变化:",{prevTag:t,currentTag:u.value,hotTags:c.value}),i("update:modelValue",u.value),i("tagSelected",u.value)})(e.name)},{default:v((()=>[E(w(e.name)+" ("+w(e.count)+") ",1)])),_:2},1032,["type","onClick"])))),128))])):C("",!0)}}),[["__scopeId","data-v-8a1791fe"]]),vs={class:"toggle-button-container"},ps={class:"toggle-card-front"},gs={class:"toggle-card-back"},hs=Oa(i({__name:"ToggleButton",props:{value:{type:Boolean,required:!0}},emits:["update:value","toggle"],setup(e,{emit:t}){const a=e,l=t,i=n(!1),s=n(null),c=n(null),u=o((()=>a.value?"评":"文")),d=o((()=>a.value?"文":"评")),b=()=>{l("update:value",!a.value),l("toggle"),s.value&&(clearTimeout(s.value),s.value=null),i.value=!0,s.value=window.setTimeout((()=>{i.value=!1,C()}),800)},k=()=>{i.value||(c.value&&(clearTimeout(c.value),c.value=null),i.value=!0,s.value=window.setTimeout((()=>{i.value=!1,C()}),800))},x=()=>{i.value||C()},C=()=>{c.value&&(clearTimeout(c.value),c.value=null);const e=8e3+12e3*Math.random();c.value=window.setTimeout((()=>{i.value?C():(i.value=!0,s.value=window.setTimeout((()=>{i.value=!1,C()}),800))}),e)};return r((()=>{C()})),z((()=>{s.value&&(clearTimeout(s.value),s.value=null),c.value&&(clearTimeout(c.value),c.value=null)})),(e,t)=>(m(),f("div",vs,[h("div",{class:y(["toggle-card",{"is-flipping":i.value}]),onMouseenter:k,onMouseleave:x,onClick:b,ref:"toggleButtonRef"},[h("div",ps,[p(g(ge),{type:"info",class:"cursor-pointer",size:32},{default:v((()=>[E(w(u.value),1)])),_:1})]),h("div",gs,[p(g(ge),{type:"info",class:"cursor-pointer",size:32},{default:v((()=>[E(w(d.value),1)])),_:1})])],34)]))}}),[["__scopeId","data-v-8934ce6a"]]),fs={URL:"/core/user-privileges",generateCode:async e=>(await Sa(fs.URL+"/code",e)).data,activate:async e=>(await Sa(fs.URL+"/activation",e)).data,search:async(e,t)=>(await La(fs.URL,e,{signal:t})).data},ws={URL:"/core/user-privilege-templates",search:async e=>(await La(ws.URL,{name:e})).data,save:async e=>(await Sa(ws.URL,e)).data},ys={...fs,generatePrivilegeCode:async e=>(await Sa("/api/user-privilege/generate-code",e)).data,searchUsers:async e=>(await La("/api/user/search",{keyword:e})).data,searchTemplates:async e=>(await La("/api/user-privilege-template/search",{keyword:e})).data},bs={...ws,savePrivilegeTemplate:async e=>(await Sa("/api/user-privilege-template/save",e)).data},ks={class:"privilege-modal"},xs={class:"tab-content"},Cs={key:0,class:"generated-code-section"},Ls={class:"code-display"},Ss={class:"modal-actions"},Ts={class:"tab-content"},Ms={class:"modal-actions"},Rs=Oa(i({__name:"PrivilegeModal",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(e,{expose:t,emit:a}){const l=e,i=a,r=Pe(),s=o({get:()=>l.modelValue,set:e=>i("update:modelValue",e)}),c=n("code"),u=n(),w=n(),y=A({userId:0,privilegeType:0,templateId:void 0,amount:void 0,expireTime:null,verificationType:0}),b=n(null),k=A({name:"",icon:"",description:"",link:"",amount:0,verificationType:0}),x=n(!1),L=n(!1),S=n(""),T=n([]),M=n(!1),R=n([]),_=n(!1),I={userId:{required:!0,message:"请选择用户",type:"number"},privilegeType:{required:!0,message:"请选择特权类型",type:"number"},templateId:{required:!0,validator:(e,t)=>!(0===y.privilegeType&&!t)||new Error("请选择模板")},amount:{required:!0,validator:(e,t)=>!(1===y.privilegeType&&(!t||t<=0))||new Error("请输入有效的付费面额")},expireTime:{required:!0,message:"请选择过期时间"},verificationType:{required:!0,message:"请选择验证类型",type:"number"}},z={name:{required:!0,message:"请输入特权名称"},icon:{required:!0,message:"请上传特权图标"},description:{required:!0,message:"请输入特权描述"},link:{required:!0,message:"请输入特权链接"},amount:{required:!0,message:"请输入付费面额",type:"number"},verificationType:{required:!0,message:"请选择验证类型",type:"number"}},P=async e=>{if(e){M.value=!0;try{const t=await ys.searchUsers(e);t.success&&t.data&&(T.value=t.data.map((e=>({label:e.username,value:e.id,avatar:e.avatar}))))}catch(t){}finally{M.value=!1}}else T.value=[]},B=async e=>{if(e){_.value=!0;try{const t=await ys.searchTemplates(e);t.success&&t.data&&(R.value=t.data.map((e=>({label:e.name,value:e.id}))))}catch(t){}finally{_.value=!1}}else R.value=[]},$=async()=>{if(u.value){try{await u.value.validate()}catch{return}x.value=!0;try{const e={...y,expireTime:b.value?new Date(b.value).toISOString():null},t=await ys.generatePrivilegeCode(e);t.success&&t.data?(S.value=t.data,r.success("激活码生成成功！")):r.error("激活码生成失败")}catch(e){r.error("生成激活码失败，请稍后重试")}finally{x.value=!1}}},D=async()=>{if(w.value){try{await w.value.validate()}catch{return}L.value=!0;try{(await bs.savePrivilegeTemplate(k)).success?(r.success("模板创建成功！"),s.value=!1,i("success"),O()):r.error("模板创建失败")}catch(e){r.error("保存模板失败，请稍后重试")}finally{L.value=!1}}},U=e=>{if(e.fileList.length>0){const t=e.fileList[0];t.url&&(k.icon=t.url)}else k.icon=""},V=async()=>{try{await navigator.clipboard.writeText(S.value),r.success("激活码已复制到剪贴板")}catch(e){r.error("复制失败，请手动复制")}},F=()=>{s.value=!1,O()},O=()=>{Object.assign(y,{userId:0,privilegeType:0,templateId:void 0,amount:void 0,expireTime:null,verificationType:0}),b.value=null,Object.assign(k,{name:"",icon:"",description:"",link:"",amount:0,verificationType:0}),S.value="",c.value="code"};return t({open:()=>{s.value=!0},close:()=>{s.value=!1},reset:()=>{O()},resetForms:O}),(e,t)=>(m(),d(g(re),{show:s.value,"onUpdate:show":t[12]||(t[12]=e=>s.value=e),preset:"dialog",title:"特权管理","mask-closable":!1,style:{width:"600px"}},{default:v((()=>[h("div",ks,[p(g(Ie),{value:c.value,"onUpdate:value":t[11]||(t[11]=e=>c.value=e),type:"line",animated:""},{default:v((()=>[p(g(Be),{name:"code",tab:"激活码"},{default:v((()=>[h("div",xs,[p(g(ce),{ref_key:"codeFormRef",ref:u,model:y,rules:I,"label-placement":"left","label-width":"100px"},{default:v((()=>[p(g(ue),{label:"用户",path:"userId"},{default:v((()=>[p(g(te),{value:y.userId,"onUpdate:value":t[0]||(t[0]=e=>y.userId=e),placeholder:"搜索用户名",filterable:"",remote:"",options:T.value,loading:M.value,onSearch:P,clearable:""},null,8,["value","options","loading"])])),_:1}),p(g(ue),{label:"特权类型",path:"privilegeType"},{default:v((()=>[p(g(ve),{value:y.privilegeType,"onUpdate:value":t[1]||(t[1]=e=>y.privilegeType=e)},{default:v((()=>[p(g($e),{value:0},{default:v((()=>t[13]||(t[13]=[E("模板")]))),_:1}),p(g($e),{value:1},{default:v((()=>t[14]||(t[14]=[E("付费")]))),_:1})])),_:1},8,["value"])])),_:1}),0===y.privilegeType?(m(),d(g(ue),{key:0,label:"模板",path:"templateId"},{default:v((()=>[p(g(te),{value:y.templateId,"onUpdate:value":t[2]||(t[2]=e=>y.templateId=e),placeholder:"搜索模板名称",filterable:"",remote:"",options:R.value,loading:_.value,onSearch:B,clearable:""},null,8,["value","options","loading"])])),_:1})):C("",!0),1===y.privilegeType?(m(),d(g(ue),{key:1,label:"付费面额",path:"amount"},{default:v((()=>[p(g(De),{value:y.amount,"onUpdate:value":t[3]||(t[3]=e=>y.amount=e),placeholder:"请输入付费面额",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})):C("",!0),p(g(ue),{label:"过期时间",path:"expireTime"},{default:v((()=>[p(g(Ue),{value:b.value,"onUpdate:value":t[4]||(t[4]=e=>b.value=e),type:"datetime",placeholder:"选择过期时间",style:{width:"100%"}},null,8,["value"])])),_:1}),p(g(ue),{label:"验证类型",path:"verificationType"},{default:v((()=>[p(g(ve),{value:y.verificationType,"onUpdate:value":t[5]||(t[5]=e=>y.verificationType=e)},{default:v((()=>[p(g($e),{value:0},{default:v((()=>t[15]||(t[15]=[E("短信验证")]))),_:1}),p(g($e),{value:1},{default:v((()=>t[16]||(t[16]=[E("二维码验证")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1},8,["model"]),S.value?(m(),f("div",Cs,[p(g(Ve),null,{default:v((()=>t[17]||(t[17]=[E("生成的激活码")]))),_:1}),h("div",Ls,[p(g(oe),{value:S.value,readonly:"",placeholder:"激活码将在这里显示"},{suffix:v((()=>[p(g(ne),{text:"",onClick:V},{icon:v((()=>[p(g(Wl))])),_:1})])),_:1},8,["value"])])])):C("",!0),h("div",Ss,[p(g(ne),{onClick:F,disabled:x.value},{default:v((()=>t[18]||(t[18]=[E("取消")]))),_:1},8,["disabled"]),p(g(ne),{type:"primary",onClick:$,loading:x.value},{default:v((()=>t[19]||(t[19]=[E(" CODE ")]))),_:1},8,["loading"])])])])),_:1}),p(g(Be),{name:"template",tab:"模板创建"},{default:v((()=>[h("div",Ts,[p(g(ce),{ref_key:"templateFormRef",ref:w,model:k,rules:z,"label-placement":"left","label-width":"100px"},{default:v((()=>[p(g(ue),{label:"特权名称",path:"name"},{default:v((()=>[p(g(oe),{value:k.name,"onUpdate:value":t[6]||(t[6]=e=>k.name=e),placeholder:"请输入特权名称"},null,8,["value"])])),_:1}),p(g(ue),{label:"特权图标",path:"icon"},{default:v((()=>[p(g(Fe),{"default-file-list":[],max:1,"list-type":"image-card",onChange:U},{default:v((()=>t[20]||(t[20]=[E(" 上传图标 ")]))),_:1})])),_:1}),p(g(ue),{label:"特权描述",path:"description"},{default:v((()=>[p(g(oe),{value:k.description,"onUpdate:value":t[7]||(t[7]=e=>k.description=e),type:"textarea",placeholder:"请输入特权描述",rows:3},null,8,["value"])])),_:1}),p(g(ue),{label:"特权链接",path:"link"},{default:v((()=>[p(g(oe),{value:k.link,"onUpdate:value":t[8]||(t[8]=e=>k.link=e),placeholder:"请输入特权链接"},null,8,["value"])])),_:1}),p(g(ue),{label:"付费面额",path:"amount"},{default:v((()=>[p(g(De),{value:k.amount,"onUpdate:value":t[9]||(t[9]=e=>k.amount=e),placeholder:"请输入付费面额",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1}),p(g(ue),{label:"验证类型",path:"verificationType"},{default:v((()=>[p(g(ve),{value:k.verificationType,"onUpdate:value":t[10]||(t[10]=e=>k.verificationType=e)},{default:v((()=>[p(g($e),{value:0},{default:v((()=>t[21]||(t[21]=[E("短信验证")]))),_:1}),p(g($e),{value:1},{default:v((()=>t[22]||(t[22]=[E("二维码验证")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1},8,["model"]),h("div",Ms,[p(g(ne),{onClick:F,disabled:L.value},{default:v((()=>t[23]||(t[23]=[E("取消")]))),_:1},8,["disabled"]),p(g(ne),{type:"primary",onClick:D,loading:L.value},{default:v((()=>t[24]||(t[24]=[E(" 创建 ")]))),_:1},8,["loading"])])])])),_:1})])),_:1},8,["value"])])])),_:1},8,["show"]))}}),[["__scopeId","data-v-4d0b4db8"]]),As=Object.freeze(Object.defineProperty({__proto__:null,default:Rs},Symbol.toStringTag,{value:"Module"}));function Es(){const e=Pe(),t=n(!1),a=n(!1),l=n(null),i=n([]),r=n({searchKey:"",owner:!1,interaction:!1,favorite:!1,tag:""}),s=o((()=>Object.entries(r.value).some((([e,t])=>"tag"===e?!!t:"boolean"==typeof t?t:"searchKey"===e&&!!t)))),{searchComments:c,searchArticles:u}=function(e,t,a,l,n){const i=Pe();return{searchComments:async o=>{var r;if(ta.debug("searchComments 被调用:",{hasSearchCondition:l.value,searchCondition:a.value,commentDanmakuRef:!!o}),!l.value)return o&&(o.clearDanmaku(),o.danmakuLoop=!1),void ta.debug("没有搜索条件，清空弹幕");t.value=!0,e.value=!0;try{const e=await rr.search(a.value,null==(r=n.value)?void 0:r.signal);if(ta.debug("搜索评论API响应:",e),o){const t=Array.isArray(e.data)?e.data:[];ta.debug("准备添加评论到弹幕:",{count:t.length,comments:t}),o.addCommentList(t)}}catch(s){"CanceledError"!==s.name&&"canceled"!==s.message&&i.error("加载评论失败，请稍后重试")}finally{e.value=!1,t.value=!1,n.value=null}},searchArticles:async(l,o=!1)=>{if(ta.debug("searchArticles called:",{loadMore:o,hasRef:!!l,isSearching:t.value,isLoading:e.value,searchCondition:a.value}),!o)if(l){t.value=!0,e.value=!0;try{l.resetList(),ta.debug("loadArticles completed")}catch(r){ta.error("searchArticles error:",r),"CanceledError"!==r.name&&"canceled"!==r.message&&i.error("加载文章失败，请稍后重试")}finally{e.value=!1,t.value=!1,n.value=null}}else ta.warn("articleListRef.value is null/undefined")}}}(t,a,r,s,l),d=()=>{ia.set(To,r.value),ta.debug("保存搜索条件:",r.value)},m=(t,n,o,s=!1)=>{a.value?ta.warn("搜索被阻止：正在进行其他搜索"):(l.value&&(l.value.abort(),l.value=null),l.value=new AbortController,i.value.push({timestamp:Date.now(),condition:{...r.value},type:t?"article":"comment"}),i.value.length>10&&i.value.shift(),ta.debug("触发搜索:",{isCardVisible:t,condition:r.value,loadMore:s}),ba("unified_search",(()=>{a.value=!0;try{t?n?u(n,s):(ta.warn("articleListRef.value is null/undefined"),a.value=!1):o?c(o):(ta.warn("commentDanmakuRef.value is null/undefined"),a.value=!1)}catch(l){ta.error("搜索过程中发生错误:",l),e.error("搜索失败，请稍后重试")}finally{setTimeout((()=>{a.value=!1}),500)}}),200))};return{isLoading:t,isSearching:a,searchCondition:r,hasSearchCondition:s,searchHistory:i,getSearchPlaceholder:e=>e?"感兴趣的文章":"有意思的评论",loadSearchCondition:()=>{const e=ia.get(To);e&&(r.value=e,ta.debug("加载搜索条件:",e))},saveSearchCondition:d,search:m,handleTagSelected:(e,t,a,l)=>{r.value.tag=e,ta.debug("标签选择:",{tagName:e,isCardVisible:t,currentCondition:r.value}),d(),m(t,a,l)},cleanup:()=>{l.value&&(l.value.abort(),l.value=null),i.value=[],ta.debug("清理搜索状态")}}}const _s={beforeEnter:e=>{e instanceof HTMLElement&&(e.style.opacity="0",e.style.transform="translateY(2px)")},enter:(e,t)=>{e instanceof HTMLElement?(e.offsetHeight,e.style.transition="opacity 0.2s ease, transform 0.2s ease",e.style.opacity="1",e.style.transform="translateY(0)",setTimeout(t,200)):t()},leave:(e,t)=>{e instanceof HTMLElement?(e.style.transition="opacity 0.2s ease, transform 0.2s ease",e.style.opacity="0",e.style.transform="translateY(-2px)",setTimeout(t,200)):t()}},Is={class:"home-layout"},zs={class:"home-layout-top"},Ps={class:"left-controls-container"},Bs={class:"control-item"},$s={class:"control-item"},Ds={class:"middle-controls-container"},Us={class:"tag-bar-wrapper"},Vs={class:"home-layout-content"},Fs=Oa(i({__name:"Home",props:{initialSearchCondition:{default:void 0},autoLoad:{type:Boolean,default:!0}},emits:["search-condition-change","view-mode-change","article-create-success","privilege-create-success"],setup(e,{expose:t,emit:a}){const l=W((()=>Ar((()=>import("./ActivationCodeModal-5g0uANuV.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58])))),i=e,o=a,u=Es(),w=function(){const e=n(!1),t=n(!1);return{danmakuLoop:e,danmakuPause:t,handleDanmakuPauseChange:(e,t)=>{t&&(e?t.pause():t.play())},resetDanmakuState:()=>{e.value=!1,t.value=!1},handleDanmakuSubscription:(e,t)=>{e.value&&(t?e.value.subscribeComment():(e.value.unsubscribeComment(),e.value.clearDanmaku()))},handleDanmakuResize:(e,t)=>{!t&&e.value&&e.value.resize()}}}(),y=function(){const e=n(!0),t=n(0),a=n(null),l=n(null),i=n(null),o=async(e,t=1e3)=>{const a=Date.now();for(;!e()&&Date.now()-a<t;)await new Promise((e=>setTimeout(e,10)));return!!e()};return{isCardVisible:e,onlineCount:t,articleModalRef:a,articleListRef:l,commentDanmakuRef:i,initializeState:()=>{go.connect();const t=ia.get(So);null!=t&&(e.value=Boolean(t))},toggleCardVisibility:(t,a,n)=>{ia.set(So,Boolean(e.value)),e.value?(n(i,!1),t(),c((async()=>{await o((()=>l.value))&&a()}))):(n(i,!0),c((async()=>{await o((()=>i.value))&&a()})))},resetArticleList:()=>{l.value&&l.value.resetList()},openCreateArticleDialog:()=>{var e;null==(e=a.value)||e.openCreateArticleDialog()},createResizeCallback:t=>()=>{t(i,e.value)},cleanup:e=>{window.removeEventListener("resize",e)}}}(),b=n(null),k=n(null),x=n(!1),C=n(!1);s(b,(e=>{y.articleListRef.value=e}),{immediate:!0}),s(k,(e=>{y.commentDanmakuRef.value=e}),{immediate:!0});const L=y.createResizeCallback(w.handleDanmakuResize);r((async()=>{y.initializeState(),i.initialSearchCondition?(u.searchCondition.value={...u.searchCondition.value,...i.initialSearchCondition},u.saveSearchCondition()):u.loadSearchCondition(),await c(),i.autoLoad&&setTimeout((async()=>{await S()}),50),window.addEventListener("resize",L)})),z((()=>{u.cleanup(),y.cleanup(L)}));const S=async(e=!1)=>{if(ta.debug("执行搜索操作:",{isCardVisible:y.isCardVisible.value,articleListRef:y.articleListRef.value,commentDanmakuRef:y.commentDanmakuRef.value,loadMore:e}),await c(),!y.isCardVisible.value){let e=0;const t=20;for(;!y.commentDanmakuRef.value&&e<t;)await new Promise((e=>setTimeout(e,100))),e++,ta.debug(`等待弹幕组件准备就绪，重试次数: ${e}`);if(!y.commentDanmakuRef.value)return void ta.warn("弹幕组件在等待时间内未能准备就绪，跳过搜索")}u.search(y.isCardVisible.value,y.articleListRef.value,y.commentDanmakuRef.value,e),o("search-condition-change",u.searchCondition.value)},T=()=>{y.toggleCardVisibility(y.resetArticleList,S,w.handleDanmakuSubscription),o("view-mode-change",y.isCardVisible.value)},M=()=>{x.value=!0},R=async()=>{y.resetArticleList(),await S(),o("privilege-create-success")},A=()=>{C.value=!0},_=()=>{};return t({performSearch:async(e=!1)=>{await S(e)},resetArticleList:()=>{y.resetArticleList()},toggleViewMode:()=>{T()},getCurrentSearchCondition:()=>u.searchCondition.value}),(e,t)=>(m(),f("div",Is,[h("div",zs,[p(V,{name:"fade-slide"},{default:v((()=>[$(h("div",Ps,[h("div",Bs,[p(g(ge),{type:"info",class:"control-label"},{default:v((()=>t[9]||(t[9]=[E("循环：")]))),_:1}),p(g(Oe),{value:g(w).danmakuLoop.value,"onUpdate:value":t[0]||(t[0]=e=>g(w).danmakuLoop.value=e),size:"small"},null,8,["value"])]),h("div",$s,[p(g(ge),{type:"info",class:"control-label"},{default:v((()=>t[10]||(t[10]=[E("暂停：")]))),_:1}),p(g(Oe),{value:g(w).danmakuPause.value,"onUpdate:value":[t[1]||(t[1]=e=>g(w).danmakuPause.value=e),t[2]||(t[2]=e=>g(w).handleDanmakuPauseChange(e,g(y).commentDanmakuRef.value))],size:"small"},null,8,["value"])])],512),[[D,!g(y).isCardVisible.value]])])),_:1}),h("div",Ds,[p(hs,{value:g(y).isCardVisible.value,"onUpdate:value":t[3]||(t[3]=e=>g(y).isCardVisible.value=e),onToggle:T},null,8,["value"]),p(us,{modelValue:g(u).searchCondition.value,"onUpdate:modelValue":t[4]||(t[4]=e=>g(u).searchCondition.value=e),placeholder:g(u).getSearchPlaceholder(g(y).isCardVisible.value),onSearch:S},null,8,["modelValue","placeholder"]),p(ss,{onClick:M,onLongPress:A})]),p(Rs,{modelValue:x.value,"onUpdate:modelValue":t[5]||(t[5]=e=>x.value=e),onSuccess:R},null,8,["modelValue"]),p(g(l),{modelValue:C.value,"onUpdate:modelValue":t[6]||(t[6]=e=>C.value=e),onSuccess:_},null,8,["modelValue"]),p(Uo)]),h("div",Us,[p(ms,{modelValue:g(u).searchCondition.value.tag,"onUpdate:modelValue":t[7]||(t[7]=e=>g(u).searchCondition.value.tag=e),onTagSelected:t[8]||(t[8]=e=>g(u).handleTagSelected(e,g(y).isCardVisible.value,g(y).articleListRef.value,g(y).commentDanmakuRef.value))},null,8,["modelValue"])]),h("div",Vs,[p(V,{onBeforeEnter:g(_s).beforeEnter,onEnter:g(_s).enter,onLeave:g(_s).leave,duration:{enter:200,leave:200},mode:"out-in"},{default:v((()=>[g(y).isCardVisible.value?(m(),d(Nr,{key:"article","search-condition":g(u).searchCondition,ref_key:"articleListRef",ref:b,onReset:g(y).resetArticleList},null,8,["search-condition","onReset"])):(m(),d(rs,{key:"comment","search-condition":g(u).searchCondition.value,loop:g(w).danmakuLoop.value,pause:g(w).danmakuPause.value,ref_key:"commentDanmakuRef",ref:k},null,8,["search-condition","loop","pause"]))])),_:1},8,["onBeforeEnter","onEnter","onLeave"])])]))}}),[["__scopeId","data-v-db2411d0"]]),Os="/assets/logo-CJOwX2dT.png",Hs=i({__name:"TurnstileVerification",props:{sitekey:{default:()=>Gt.turnstile.siteKey},theme:{default:"auto"},size:{default:"normal"},retry:{default:"auto"},retryInterval:{default:8e3},refreshExpired:{default:"auto"},language:{default:"auto"},execution:{default:"render"},appearance:{default:"always"},responseField:{type:Boolean,default:!0},responseFieldName:{default:"cf-turnstile-response"},cData:{default:""}},emits:["success","error","expired"],setup(e,{expose:t,emit:a}){const l=e,i=a,o=n();let c=null;const u=async()=>{try{if(await(()=>{const e="https://challenges.cloudflare.com/turnstile/v0/api.js";return window.__TURNSTILE_SCRIPT_LOADED__&&window.turnstile?Promise.resolve():window.__TURNSTILE_SCRIPT_LOADING__?new Promise((e=>{const t=setInterval((()=>{window.__TURNSTILE_SCRIPT_LOADED__&&window.turnstile&&(clearInterval(t),e())}),100)})):window.turnstile?(window.__TURNSTILE_SCRIPT_LOADED__=!0,Promise.resolve()):document.querySelector(`script[src="${e}"]`)?(window.__TURNSTILE_SCRIPT_LOADING__=!0,new Promise((e=>{const t=setInterval((()=>{window.turnstile&&(clearInterval(t),window.__TURNSTILE_SCRIPT_LOADING__=!1,window.__TURNSTILE_SCRIPT_LOADED__=!0,e())}),100)}))):(window.__TURNSTILE_SCRIPT_LOADING__=!0,new Promise(((t,a)=>{const l=document.createElement("script");l.src=e,l.async=!0,l.defer=!0,l.onload=()=>{window.__TURNSTILE_SCRIPT_LOADING__=!1,window.__TURNSTILE_SCRIPT_LOADED__=!0,ta.debug("Turnstile script loaded successfully"),t()},l.onerror=()=>{window.__TURNSTILE_SCRIPT_LOADING__=!1,ta.error("Failed to load Turnstile script"),a(new Error("Failed to load Turnstile script"))},document.head.appendChild(l)})))})(),!window.turnstile||!o.value)return void ta.error("Turnstile not available or container not found");c&&(window.turnstile.remove(c),c=null);const e={sitekey:l.sitekey,callback:e=>{ta.debug("Turnstile verification successful"),i("success",e)},"error-callback":()=>{ta.error("Turnstile verification failed"),i("error")},"expired-callback":()=>{ta.warn("Turnstile verification expired"),i("expired")},theme:l.theme,size:l.size,retry:l.retry,"retry-interval":l.retryInterval,"refresh-expired":l.refreshExpired,language:l.language,execution:l.execution,appearance:l.appearance,"response-field":l.responseField,"response-field-name":l.responseFieldName,cData:l.cData};c=window.turnstile.render(o.value,e),ta.debug("Turnstile widget rendered with ID:",c)}catch(e){ta.error("Error rendering Turnstile:",e),i("error")}},d=()=>{window.turnstile&&c&&(window.turnstile.remove(c),c=null,ta.debug("Turnstile widget removed"))};return t({reset:()=>{window.turnstile&&c&&(window.turnstile.reset(c),ta.debug("Turnstile widget reset"))},remove:d,getResponse:()=>window.turnstile&&c?window.turnstile.getResponse(c):"",isExpired:()=>!(!window.turnstile||!c)&&window.turnstile.isExpired(c)}),s((()=>l.sitekey),(()=>{l.sitekey&&u()})),r((()=>{l.sitekey&&u()})),z((()=>{d()})),(e,t)=>(m(),f("div",{ref_key:"turnstileContainer",ref:o,class:"turnstile-container"},null,512))}});var js=(e=>(e.LOGIN="login",e.REGISTER="register",e.FORGOT="forgot",e))(js||{});const Ns={class:"email-code-container"},qs=Oa(i({__name:"EmailCodeInput",props:{value:{},emailCodeState:{},disabled:{type:Boolean,default:!1},buttonType:{default:"info"}},emits:["update:value","send-code","keyup.enter"],setup(e,{emit:t}){const a=e,l=t,n=o({get:()=>a.value,set:e=>l("update:value",e)});return(e,t)=>(m(),f("div",Ns,[p(g(oe),{class:"login-form-ipt email-code-input",value:n.value,"onUpdate:value":t[0]||(t[0]=e=>n.value=e),placeholder:"请输入验证码",maxlength:"6",onKeyup:t[1]||(t[1]=F((t=>e.$emit("keyup.enter")),["enter"]))},null,8,["value"]),p(g(ne),{class:"send-code-btn",disabled:e.emailCodeState.sendCodeDisabled||e.disabled,onClick:t[2]||(t[2]=t=>e.$emit("send-code")),text:"",type:e.buttonType,size:"small"},{default:v((()=>[E(w(e.emailCodeState.sendCodeText),1)])),_:1},8,["disabled","type"])]))}}),[["__scopeId","data-v-31eb16cb"]]),Ys={class:"login-mode-switch"},Ws=Oa(i({__name:"LoginModeSwitch",props:{loginMode:{}},emits:["update:login-mode"],setup(e,{emit:t}){const a=e,l=t,n=o({get:()=>a.loginMode,set:e=>l("update:login-mode",e)});return(e,t)=>(m(),f("div",Ys,[p(g(Ie),{value:n.value,"onUpdate:value":t[0]||(t[0]=e=>n.value=e),type:"segment",size:"small",class:"login-tabs"},{default:v((()=>[p(g(Be),{name:"phone",tab:"手机号登录"}),p(g(Be),{name:"email",tab:"邮箱登录"})])),_:1},8,["value"])]))}}),[["__scopeId","data-v-80bb11d5"]]),Js={class:"forgot-password-link"},Ks={class:"login-form-btn"},Xs=Oa(i({__name:"LoginForm",props:{form:{},loginMode:{},emailCodeState:{},isValidEmail:{type:Boolean},isTurnstileVerified:{type:Boolean},loading:{type:Boolean}},emits:["update:form","update:login-mode","login","flip-card","show-forgot-password","send-email-code","turnstile-success","turnstile-error"],setup(e,{expose:t,emit:a}){const l=e,i=a,r=n(null),c=n(),u=o({get:()=>l.form,set:e=>i("update:form",e)}),w=o({get:()=>l.loginMode,set:e=>i("update:login-mode",e)}),y=o((()=>{const e={};return"phone"===l.loginMode?(e.phone=[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号",trigger:"blur"}],e.password=[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少需要6个字符",trigger:"blur"}]):"email"===l.loginMode&&(e.email=[{required:!0,message:"邮箱不能为空",trigger:"blur"},{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"请输入有效的邮箱地址",trigger:"blur"}],e.emailCode=[{required:!0,message:"请输入邮箱验证码",trigger:"blur"},{pattern:/^\d{6}$/,message:"验证码格式不正确",trigger:"blur"}]),e})),b=e=>{u.value.phone=e.replace(/\D/g,"")};s((()=>l.loginMode),(()=>{r.value&&r.value.restoreValidation()}));return t({validate:e=>{var t;return null==(t=r.value)?void 0:t.validate(e)},restoreValidation:()=>{var e;null==(e=r.value)||e.restoreValidation()},resetTurnstile:()=>{var e;null==(e=c.value)||e.reset()}}),(e,t)=>(m(),d(g(ce),{"label-placement":"left",model:u.value,rules:y.value,ref_key:"formRef",ref:r,"label-width":90,class:"login-form"},{default:v((()=>[p(Ws,{"login-mode":w.value,"onUpdate:loginMode":t[0]||(t[0]=e=>w.value=e)},null,8,["login-mode"]),"phone"===w.value?(m(),f(L,{key:0},[p(g(ue),{label:"手机号",path:"phone"},{default:v((()=>[p(g(oe),{class:"login-form-ipt",value:u.value.phone,"onUpdate:value":t[1]||(t[1]=e=>u.value.phone=e),placeholder:"请输入手机号",maxlength:"11",onInput:b,onKeyup:t[2]||(t[2]=F((t=>e.$emit("login")),["enter"]))},null,8,["value"])])),_:1}),p(g(ue),{label:"密码",path:"password"},{default:v((()=>[p(g(oe),{class:"login-form-ipt",value:u.value.password,"onUpdate:value":t[3]||(t[3]=e=>u.value.password=e),type:"password",placeholder:"请输入密码","show-password-on":"click",onKeyup:t[4]||(t[4]=F((t=>e.$emit("login")),["enter"]))},null,8,["value"])])),_:1})],64)):C("",!0),"email"===w.value?(m(),f(L,{key:1},[p(g(ue),{label:"邮箱",path:"email"},{default:v((()=>[p(g(oe),{class:"login-form-ipt",value:u.value.email,"onUpdate:value":t[5]||(t[5]=e=>u.value.email=e),placeholder:"请输入邮箱地址",onKeyup:t[6]||(t[6]=F((t=>e.$emit("login")),["enter"]))},null,8,["value"])])),_:1}),p(g(ue),{label:"验证码",path:"emailCode"},{default:v((()=>[p(qs,{value:u.value.emailCode,"onUpdate:value":t[7]||(t[7]=e=>u.value.emailCode=e),"email-code-state":e.emailCodeState,disabled:!e.isValidEmail||!e.isTurnstileVerified,onSendCode:t[8]||(t[8]=t=>e.$emit("send-email-code",g(js).LOGIN)),onKeyup:t[9]||(t[9]=F((t=>e.$emit("login")),["enter"]))},null,8,["value","email-code-state","disabled"])])),_:1})],64)):C("",!0),p(Hs,{ref_key:"turnstileRef",ref:c,onSuccess:t[10]||(t[10]=t=>e.$emit("turnstile-success",t)),onError:t[11]||(t[11]=t=>e.$emit("turnstile-error"))},null,512),h("div",Js,[p(g(ne),{text:"",type:"tertiary",onClick:t[12]||(t[12]=t=>e.$emit("show-forgot-password")),size:"small"},{default:v((()=>t[15]||(t[15]=[E(" 忘记密码？ ")]))),_:1})]),h("div",Ks,[p(g(ne),{class:"login-btn",type:"info",onClick:t[13]||(t[13]=t=>e.$emit("login")),loading:e.loading},{default:v((()=>t[16]||(t[16]=[E(" 登录 ")]))),_:1},8,["loading"]),p(g(ne),{class:"flip-btn",onClick:t[14]||(t[14]=t=>e.$emit("flip-card"))},{default:v((()=>t[17]||(t[17]=[E("注册")]))),_:1})])])),_:1},8,["model","rules"]))}}),[["__scopeId","data-v-3402c077"]]),Gs={class:"register-form-btn"},Zs=Oa(i({__name:"RegisterForm",props:{form:{},emailCodeState:{},isValidEmail:{type:Boolean},isTurnstileVerified:{type:Boolean}},emits:["update:form","register","flip-card","send-email-code","turnstile-success","turnstile-error"],setup(e,{expose:t,emit:a}){const l=e,i=a,r=n(null),s=n(),c=o({get:()=>l.form,set:e=>i("update:form",e)}),u={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:10,message:"用户名长度在 3 到 10 个字符之间",trigger:"blur"}],phone:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号",trigger:"blur"}],email:[{required:!0,message:"邮箱不能为空",trigger:"blur"},{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"请输入有效的邮箱地址",trigger:"blur"}],emailCode:[{required:!0,message:"请输入邮箱验证码",trigger:"blur"},{pattern:/^\d{6}$/,message:"验证码格式不正确",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少需要6个字符",trigger:"blur"}],reenteredPassword:[{required:!0,message:"请再次输入密码",trigger:["input","blur"]},{validator:(e,t)=>!!l.form.password&&l.form.password.startsWith(t)&&l.form.password.length>=t.length,message:"两次密码输入不一致",trigger:"input"},{validator:(e,t)=>t===l.form.password,message:"两次密码输入不一致",trigger:["blur","password-input"]}],job:[{required:!0,message:"请输入职业",trigger:"blur"},{min:2,max:8,message:"职业名长度在 2 到 8 个字符之间",trigger:"blur"}]},f=e=>c.value.phone=e.replace(/\D/g,"");return t({validate:e=>{var t;return null==(t=r.value)?void 0:t.validate(e)},restoreValidation:()=>{var e;null==(e=r.value)||e.restoreValidation()},resetTurnstile:()=>{var e;null==(e=s.value)||e.reset()}}),(e,t)=>(m(),d(g(ce),{"label-placement":"left",model:c.value,rules:u,ref_key:"formRef",ref:r,"label-width":100,class:"register-form"},{default:v((()=>[p(g(ue),{label:"用户名",path:"username"},{default:v((()=>[p(g(oe),{class:"register-form-ipt",value:c.value.username,"onUpdate:value":t[0]||(t[0]=e=>c.value.username=e),placeholder:"请输入用户名",onKeyup:t[1]||(t[1]=F((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),p(g(ue),{label:"手机号",path:"phone"},{default:v((()=>[p(g(oe),{class:"register-form-ipt",maxlength:"11",value:c.value.phone,"onUpdate:value":t[2]||(t[2]=e=>c.value.phone=e),onInput:f,placeholder:"请输入手机号",onKeyup:t[3]||(t[3]=F((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),p(g(ue),{label:"邮箱",path:"email"},{default:v((()=>[p(g(oe),{class:"register-form-ipt",value:c.value.email,"onUpdate:value":t[4]||(t[4]=e=>c.value.email=e),placeholder:"请输入邮箱地址",onKeyup:t[5]||(t[5]=F((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),p(g(ue),{label:"邮箱验证码",path:"emailCode"},{default:v((()=>[p(qs,{value:c.value.emailCode,"onUpdate:value":t[6]||(t[6]=e=>c.value.emailCode=e),"email-code-state":e.emailCodeState,disabled:!e.isValidEmail||!e.isTurnstileVerified,"button-type":"primary",onSendCode:t[7]||(t[7]=t=>e.$emit("send-email-code",g(js).REGISTER)),onKeyup:t[8]||(t[8]=F((t=>e.$emit("register")),["enter"]))},null,8,["value","email-code-state","disabled"])])),_:1}),p(g(ue),{label:"密码",path:"password"},{default:v((()=>[p(g(oe),{class:"register-form-ipt",value:c.value.password,"onUpdate:value":t[9]||(t[9]=e=>c.value.password=e),type:"password",placeholder:"请输入密码","show-password-on":"click",onKeyup:t[10]||(t[10]=F((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),p(g(ue),{label:"确认密码",path:"reenteredPassword"},{default:v((()=>[p(g(oe),{class:"register-form-ipt",value:c.value.reenteredPassword,"onUpdate:value":t[11]||(t[11]=e=>c.value.reenteredPassword=e),type:"password",disabled:!c.value.password,placeholder:"请确认密码","show-password-on":"click",onKeyup:t[12]||(t[12]=F((t=>e.$emit("register")),["enter"]))},null,8,["value","disabled"])])),_:1}),p(g(ue),{label:"职业",path:"job"},{default:v((()=>[p(g(oe),{class:"register-form-ipt",value:c.value.job,"onUpdate:value":t[13]||(t[13]=e=>c.value.job=e),placeholder:"请输入职业",onKeyup:t[14]||(t[14]=F((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),p(Hs,{ref_key:"turnstileRef",ref:s,onSuccess:t[15]||(t[15]=t=>e.$emit("turnstile-success",t)),onError:t[16]||(t[16]=t=>e.$emit("turnstile-error"))},null,512),h("div",Gs,[p(g(ne),{class:"login-btn",type:"info",onClick:t[17]||(t[17]=t=>e.$emit("register"))},{default:v((()=>t[19]||(t[19]=[E("注册并登录")]))),_:1}),p(g(ne),{class:"flip-btn",onClick:t[18]||(t[18]=t=>e.$emit("flip-card"))},{default:v((()=>t[20]||(t[20]=[E("登录")]))),_:1})])])),_:1},8,["model"]))}}),[["__scopeId","data-v-a88dce1c"]]);const Qs={class:"static-elements"},ec={key:0,class:"clouds-container"},tc={key:1,class:"stars-container"},ac={class:"dynamic-elements"},lc={key:0,class:"dandelions-container"},nc={key:1,class:"fireflies-container"},ic=Oa(i({__name:"BackgroundAnimation",props:{particleCount:{type:Number,default:30,description:"动态元素的数量（蒲公英/萤火虫）"},cloudCount:{type:Number,default:9,description:"云朵数量"},enableClouds:{type:Boolean,default:!0,description:"是否显示云朵"},enableDandelions:{type:Boolean,default:!0,description:"是否显示蒲公英（浅色模式）"},enableStars:{type:Boolean,default:!0,description:"是否显示星星（暗色模式）"},enableFireflies:{type:Boolean,default:!0,description:"是否显示萤火虫（暗色模式）"},customLightGradient:{type:String,default:"",description:"自定义浅色主题背景渐变"},customDarkGradient:{type:String,default:"",description:"自定义暗色主题背景渐变"},zIndex:{type:Number,default:0,description:"背景层级（z-index）"}},emits:["theme-change"],setup(e,{expose:t,emit:a}){const l=e,n=a,{isDarkTheme:i,backgroundStyle:c}=function(e){const t=o((()=>da.value===aa.DARK)),a=o((()=>t.value?{background:(null==e?void 0:e.customDarkGradient)||"linear-gradient(to top, #0a0a0f, #121218 60%, #1c1c26 100%)",zIndex:(null==e?void 0:e.zIndex)||0}:{background:(null==e?void 0:e.customLightGradient)||"linear-gradient(to top, var(--creamy-white-3), var(--creamy-white-2) 70%, rgba(232, 240, 242, 0.8) 100%)",zIndex:(null==e?void 0:e.zIndex)||0}));return{isDarkTheme:t,backgroundStyle:a}}({customLightGradient:l.customLightGradient,customDarkGradient:l.customDarkGradient,zIndex:l.zIndex}),{getCloudStyle:u,generateCloudPseudoElementsCSS:d}={getCloudStyle:e=>{const t=e%5;let a,l,n,i,o,r,s;0===t?(a=Math.floor(150*Math.random())+350,l=Math.floor(100*Math.random())+200,n=15,i=.65,o=[{top:"25%",left:"-10%",width:"60%",height:"60%",borderRadius:"70% 60% 65% 75%"},{top:"10%",left:"30%",width:"70%",height:"70%",borderRadius:"65% 75% 60% 70%"},{top:"35%",left:"75%",width:"45%",height:"45%",borderRadius:"65% 55% 70% 60%"},{top:"60%",left:"25%",width:"55%",height:"55%",borderRadius:"70% 65% 75% 60%"},{top:"45%",left:"52%",width:"48%",height:"48%",borderRadius:"60% 75% 65% 70%"}]):1===t?(a=Math.floor(200*Math.random())+400,l=Math.floor(80*Math.random())+140,n=18,i=.6,o=[{top:"30%",left:"5%",width:"50%",height:"55%",borderRadius:"80% 70% 75% 65%"},{top:"20%",left:"40%",width:"60%",height:"70%",borderRadius:"75% 80% 65% 70%"},{top:"35%",left:"60%",width:"40%",height:"60%",borderRadius:"70% 65% 80% 75%"},{top:"25%",left:"80%",width:"35%",height:"65%",borderRadius:"65% 75% 70% 80%"}]):2===t?(a=Math.floor(120*Math.random())+280,l=Math.floor(90*Math.random())+160,n=14,i=.7,o=[{top:"20%",left:"10%",width:"55%",height:"55%",borderRadius:"65% 70% 60% 75%"},{top:"15%",left:"45%",width:"65%",height:"65%",borderRadius:"75% 65% 70% 60%"},{top:"50%",left:"25%",width:"50%",height:"50%",borderRadius:"60% 75% 65% 70%"}]):3===t?(a=Math.floor(140*Math.random())+300,l=Math.floor(120*Math.random())+180,n=16,i=.63,o=[{top:"10%",left:"5%",width:"40%",height:"40%",borderRadius:"75% 65% 70% 60%"},{top:"5%",left:"35%",width:"45%",height:"45%",borderRadius:"70% 60% 75% 65%"},{top:"15%",left:"70%",width:"35%",height:"35%",borderRadius:"65% 70% 60% 75%"},{top:"50%",left:"10%",width:"38%",height:"38%",borderRadius:"70% 75% 65% 60%"},{top:"45%",left:"40%",width:"42%",height:"42%",borderRadius:"65% 60% 75% 70%"},{top:"40%",left:"75%",width:"30%",height:"30%",borderRadius:"75% 65% 60% 70%"}]):(a=Math.floor(180*Math.random())+320,l=Math.floor(70*Math.random())+130,n=13,i=.66,o=[{top:"25%",left:"0%",width:"45%",height:"45%",borderRadius:"65% 70% 75% 60%"},{top:"20%",left:"35%",width:"50%",height:"50%",borderRadius:"75% 65% 60% 70%"},{top:"30%",left:"65%",width:"40%",height:"40%",borderRadius:"70% 60% 75% 65%"}]),0===t?(r=40,s=65):1===t?(r=10,s=30):2===t?(r=25,s=50):3===t?(r=35,s=60):(r=5,s=25);const c=a/window.innerWidth*100,u=Math.random()*(100-c),d=r+Math.random()*(s-r);let m,v;return m=0===t||3===t?8*Math.random()-4:1===t?2*Math.random()-1:4*Math.random()-2,v=4===t||1===t?6:0===t||3===t?5:4,{width:`${a}px`,height:`${l}px`,left:`${u}%`,top:`${d}%`,opacity:i,filter:`blur(${n}px)`,transform:`rotate(${m}deg)`,zIndex:v,"--cloud-type":t,"--pseudo-elements":JSON.stringify(o),border:"none",outline:"none",boxShadow:"none",backgroundColor:"transparent"}},generateCloudPseudoElementsCSS:()=>{const e=document.createElement("style");return document.querySelectorAll(".cloud").forEach(((t,a)=>{const l=t,n=l.style.getPropertyValue("--pseudo-elements");if(n)try{const t=JSON.parse(n),i=`cloud-${a}`;l.classList.add(i);let o="";t.forEach(((e,t)=>{const a=(.2*Math.random()+.4).toFixed(2),l=Math.floor(10*Math.random())+12;if(Math.floor(15*Math.random()),0===t)o+=`.${i}::before { \n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left}; \n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.8) 0%,\n                  rgba(255, 255, 255, ${a}) 40%,\n                  rgba(255, 255, 255, 0.15) 75%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${l}px);\n                box-shadow: none;\n                opacity: ${a};\n                border: none;\n                outline: none;\n              }\n`;else if(1===t)o+=`.${i}::after { \n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left}; \n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.75) 0%,\n                  rgba(255, 255, 255, ${a}) 35%,\n                  rgba(255, 255, 255, 0.1) 70%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${l}px);\n                box-shadow: none;\n                opacity: ${a};\n                border: none;\n                outline: none;\n              }\n`;else{const a=(.2*Math.random()+.35).toFixed(2),l=Math.floor(8*Math.random())+10;o+=`.${i}::before { \n                content: ''; \n                position: absolute;\n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left};\n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.7) 0%,\n                  rgba(255, 255, 255, ${a}) 30%,\n                  rgba(255, 255, 255, 0.08) 65%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${l}px);\n                box-shadow: none;\n                opacity: ${a};\n                z-index: ${t};\n                border: none;\n                outline: none;\n              }\n`}})),e.textContent+=o}catch(i){}})),document.head.appendChild(e),e}},{getStarStyle:v}={getStarStyle:e=>{const t=Math.floor(2*Math.random())+1;return{width:`${t}px`,height:`${t}px`,left:`${Math.floor(100*Math.random())}%`,top:`${Math.floor(100*Math.random())}%`,opacity:.5*Math.random()+.3,animation:e%5==0?"twinkle 3s infinite":"none"}}},{getDandelionSeedStyle:p}={getDandelionSeedStyle:e=>{const t=Math.floor(3*Math.random())+2,a=Math.floor(100*Math.random()),l=Math.floor(10*Math.random())-5,n=80+Math.floor(40*Math.random()),i=Math.floor(10*Math.random())+5,o=Math.floor(40*Math.random())-20,r=Math.floor(20*Math.random())+20;return{width:`${t}px`,height:`${t}px`,left:`${a}%`,bottom:`${l}%`,animationDelay:`${Math.floor(15*Math.random())}s`,filter:`blur(${1.5*Math.random()+.5}px)`,"--seed-type":e%3,"--animation-duration":`${r}s`,"--float-height":`${n}vh`,"--float-side":`${o}vw`,"--float-side-wave":`${i}vw`,"--rotation":Math.floor(360*Math.random())-180+"deg","--max-opacity":.2*Math.random()+.7,"--fluff-count":"1","--core-size":`${t}px`}}},{getFireflyStyle:w}={getFireflyStyle:e=>{const t=Math.floor(4*Math.random())+3,a=Math.floor(100*Math.random()),l=Math.floor(70*Math.random())+10,n=Math.floor(3*Math.random())+1,i=Math.floor(5*Math.random())+3,o=Math.floor(4*Math.random())+2,r=2*n+i+o,s=Math.floor(8*Math.random())+3,c=Math.floor(10*Math.random()),u=(30*Math.random()+10)*(Math.random()>.5?1:-1),d=(30*Math.random()+10)*(Math.random()>.5?1:-1),m=["#80ff72","#c4ff0e","#e8ff75","#00ffaa"],v=m[e%m.length],p=Math.floor(2*Math.random())+1;return{width:`${t}px`,height:`${t}px`,left:`${a}%`,top:`${l}%`,backgroundColor:v,"--glow-color":v,"--glow-size":`${Math.floor(6*Math.random())+7}px`,"--pulse-duration":`${p}s`,"--move-x":`${u}px`,"--move-y":`${d}px`,"--appear-duration":`${n}s`,"--move-duration":`${i}s`,"--pause-duration":`${o}s`,"--total-duration":`${r+s}s`,"--cycle-delay":`${s}s`,animationDelay:`${c}s`}}};return s(i,(e=>{n("theme-change",e?"dark":"light")})),r((()=>{d()})),t({isDarkTheme:i}),(t,a)=>(m(),f("div",{class:y(["background-animation",{"dark-theme":g(i)}]),style:k(g(c))},[h("div",Qs,[!g(i)&&e.enableClouds?(m(),f("div",ec,[(m(!0),f(L,null,S(Math.ceil(e.cloudCount),(e=>(m(),f("div",{key:`cloud-${e}`,class:"cloud",style:k(g(u)(e))},null,4)))),128))])):C("",!0),g(i)&&e.enableStars?(m(),f("div",tc,[(m(!0),f(L,null,S(e.particleCount,(e=>(m(),f("div",{key:`star-${e}`,class:"star",style:k(g(v)(e))},null,4)))),128))])):C("",!0)]),h("div",ac,[!g(i)&&e.enableDandelions?(m(),f("div",lc,[(m(!0),f(L,null,S(e.particleCount,(e=>(m(),f("div",{key:`dandelion-${e}`,class:"dandelion-seed",style:k(g(p)(e))},a[0]||(a[0]=[h("div",{class:"main-stem"},null,-1)]),4)))),128))])):C("",!0),g(i)&&e.enableFireflies?(m(),f("div",nc,[(m(!0),f(L,null,S(e.particleCount,(e=>(m(),f("div",{key:`firefly-${e}`,class:"firefly",style:k(g(w)(e))},null,4)))),128))])):C("",!0)])],6))}}),[["__scopeId","data-v-a753bb5a"]]),oc={class:"email-code-container"},rc={class:"turnstile-section"},sc={class:"forgot-password-actions"},cc=Oa(i({__name:"ForgotPasswordModal",props:{show:{type:Boolean}},emits:["update:show","success"],setup(e,{emit:t}){const a=e,l=t,i=n(),r=n(),c=n(!1),u=n(!1),f=n("发送验证码"),y=n(0),b=n({email:"",emailCode:"",newPassword:"",confirmPassword:""}),k=o({get:()=>a.show,set:e=>l("update:show",e)}),x=o((()=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(b.value.email))),C=n(""),L=o((()=>!!C.value)),S={email:[{required:!0,message:"邮箱不能为空",trigger:"blur"},{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"请输入有效的邮箱地址",trigger:"blur"}],emailCode:[{required:!0,message:"验证码不能为空",trigger:"blur"},{min:6,max:6,message:"验证码长度为6位",trigger:"blur"}],newPassword:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,message:"密码长度至少为 6 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(e,t)=>t===b.value.newPassword,message:"两次密码输入不一致",trigger:["blur","password-input"]}]},T=e=>{ta.debug("Turnstile 验证成功:",e),C.value=e},M=()=>{ta.debug("Turnstile 验证失败"),C.value=""},R=async()=>{var e;if(b.value.email)if(x.value)if(L.value)try{u.value=!0;const e=await xo.sendEmailCode({email:b.value.email,type:js.FORGOT});e.success?(xa.success("验证码发送成功，请查收邮件"),A()):(xa.error(e.message||"验证码发送失败"),u.value=!1)}catch{C.value="",null==(e=r.value)||e.reset(),u.value=!1}else xa.warning("请先通过验证哦~");else xa.warning("请输入有效的邮箱地址");else xa.warning("请先输入邮箱地址")},A=()=>{y.value=60,f.value=`${y.value}s后重发`;const e=setInterval((()=>{y.value--,y.value>0?f.value=`${y.value}s后重发`:(f.value="发送验证码",u.value=!1,clearInterval(e))}),1e3)},_=()=>{i.value.validate((e=>{if(!e){const e=C.value;if(!e)return void xa.warning("请先通过验证哦~");c.value=!0,xo.resetPassword({email:b.value.email,emailCode:b.value.emailCode,newPassword:b.value.newPassword,cftt:e}).then((e=>{e.success?(xa.success("密码重置成功，请使用新密码登录"),I(),l("success")):xa.error(e.message||"密码重置失败")})).catch((()=>{var e;C.value="",null==(e=r.value)||e.reset()})).finally((()=>{c.value=!1}))}}))},I=()=>{k.value=!1,b.value={email:"",emailCode:"",newPassword:"",confirmPassword:""},C.value=""};return s(k,(e=>{e||(u.value=!1,f.value="发送验证码",y.value=0)})),(e,t)=>(m(),d(g(re),{show:k.value,"onUpdate:show":t[4]||(t[4]=e=>k.value=e),preset:"dialog",title:"忘记密码"},{header:v((()=>t[5]||(t[5]=[h("div",{class:"forgot-password-header"},[h("span",null,"重置密码")],-1)]))),action:v((()=>[h("div",sc,[p(g(ne),{onClick:I},{default:v((()=>t[6]||(t[6]=[E("取消")]))),_:1}),p(g(ne),{type:"primary",onClick:_,loading:c.value},{default:v((()=>t[7]||(t[7]=[E(" 重置密码 ")]))),_:1},8,["loading"])])])),default:v((()=>[p(g(ce),{model:b.value,rules:S,ref_key:"formRef",ref:i,"label-placement":"left","label-width":100},{default:v((()=>[p(g(ue),{label:"邮箱",path:"email"},{default:v((()=>[p(g(oe),{value:b.value.email,"onUpdate:value":t[0]||(t[0]=e=>b.value.email=e),placeholder:"请输入注册时的邮箱地址"},null,8,["value"])])),_:1}),p(g(ue),{label:"验证码",path:"emailCode"},{default:v((()=>[h("div",oc,[p(g(oe),{value:b.value.emailCode,"onUpdate:value":t[1]||(t[1]=e=>b.value.emailCode=e),placeholder:"请输入邮箱验证码",maxlength:"6"},null,8,["value"]),p(g(ne),{disabled:u.value||!x.value||!L.value,onClick:R,text:"",type:"primary",size:"small",class:"send-code-btn"},{default:v((()=>[E(w(f.value),1)])),_:1},8,["disabled"])])])),_:1}),p(g(ue),{label:"新密码",path:"newPassword"},{default:v((()=>[p(g(oe),{value:b.value.newPassword,"onUpdate:value":t[2]||(t[2]=e=>b.value.newPassword=e),type:"password",placeholder:"请输入新密码","show-password-on":"click"},null,8,["value"])])),_:1}),p(g(ue),{label:"确认密码",path:"confirmPassword"},{default:v((()=>[p(g(oe),{value:b.value.confirmPassword,"onUpdate:value":t[3]||(t[3]=e=>b.value.confirmPassword=e),type:"password",placeholder:"请再次输入新密码","show-password-on":"click"},null,8,["value"])])),_:1}),h("div",rc,[p(Hs,{ref_key:"turnstileRef",ref:r,onSuccess:T,onError:M},null,512)])])),_:1},8,["model"])])),_:1},8,["show"]))}}),[["__scopeId","data-v-9d1dce3c"]]),uc={class:"layout-container"},dc={class:"header-container"},mc={class:"card-container"},vc={class:"footer-container"},pc=Oa(i({__name:"Login",props:{defaultLoginMode:{default:"phone"},showRegisterForm:{type:Boolean,default:!1}},emits:["login-success","register-success","forgot-password-success"],setup(e,{expose:t,emit:a}){const l=e,i=a,r=n({isFlipped:l.showRegisterForm,loginMode:l.defaultLoginMode,loginLoading:!1,showForgotPassword:!1}),c=n({phone:"",email:"",emailCode:"",password:""}),u=n({username:"",phone:"",email:"",emailCode:"",password:"",reenteredPassword:"",job:""}),d=n({sendCodeDisabled:!1,sendCodeText:"发送验证码",countdown:0}),w=n({loginTurnstileToken:"",registerTurnstileToken:""}),y=n(),b=n(),x=()=>{r.value.isFlipped=!r.value.isFlipped},C=e=>{ta.debug("Turnstile 验证成功:",e),r.value.isFlipped?w.value.registerTurnstileToken=e:w.value.loginTurnstileToken=e},L=()=>{ta.debug("Turnstile 验证失败"),r.value.isFlipped?w.value.registerTurnstileToken="":w.value.loginTurnstileToken="",xa.warning("验证失败，请重试")},S=o((()=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c.value.email))),T=o((()=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u.value.email))),M=o((()=>!!w.value.loginTurnstileToken)),R=o((()=>!!w.value.registerTurnstileToken));s((()=>r.value.loginMode),(()=>{c.value={phone:"",email:"",emailCode:"",password:""},d.value={sendCodeDisabled:!1,sendCodeText:"发送验证码",countdown:0}}));const A=async e=>{var t,a;let l="",n=!1;if(e===js.LOGIN?(l=c.value.email,n=M.value):e===js.REGISTER&&(l=u.value.email,n=R.value),l)if(n){if(!d.value.sendCodeDisabled)try{d.value.sendCodeDisabled=!0;const t=await xo.sendEmailCode({email:l,type:e});t.success?(xa.success("验证码发送成功，请查收邮件"),E()):(xa.error(t.message||"验证码发送失败"),d.value.sendCodeDisabled=!1)}catch{e===js.LOGIN?(w.value.loginTurnstileToken="",null==(t=y.value)||t.resetTurnstile()):(w.value.registerTurnstileToken="",null==(a=b.value)||a.resetTurnstile()),d.value.sendCodeDisabled=!1}}else xa.warning("请先通过验证哦~");else xa.warning("请先输入邮箱地址")},E=()=>{d.value.countdown=60,d.value.sendCodeText=`${d.value.countdown}s后重发`;const e=setInterval((()=>{d.value.countdown--,d.value.countdown>0?d.value.sendCodeText=`${d.value.countdown}s后重发`:(d.value.sendCodeText="发送验证码",d.value.sendCodeDisabled=!1,clearInterval(e))}),1e3)},_=()=>{const e=y.value;e&&e.validate((e=>{if(!e){const e=w.value.loginTurnstileToken;if(!e)return void xa.warning("请先通过验证哦~");r.value.loginLoading=!0;let t={cftt:e};"phone"===r.value.loginMode?t={cftt:e,phone:c.value.phone,password:c.value.password}:"email"===r.value.loginMode&&(t={cftt:e,email:c.value.email,emailCode:c.value.emailCode}),xo.login(t).then((e=>{(null==e?void 0:e.data)&&(ia.setLoginUser(e.data),ia.set(So,!0),uu.push("/"),i("login-success",e.data))})).catch((()=>{var e;w.value.loginTurnstileToken="",null==(e=y.value)||e.resetTurnstile()})).finally((()=>{r.value.loginLoading=!1}))}}))},I=()=>{const e=b.value;e&&e.validate((e=>{if(!e){const e=w.value.registerTurnstileToken;if(!e)return void xa.warning("请先通过验证哦~");xo.register({...u.value,cftt:e,confirmPassword:u.value.reenteredPassword}).then((e=>{e.data&&(ia.setLoginUser(e.data),ia.set(So,!0),uu.push("/"),i("register-success",e.data))})).catch((()=>{var e;w.value.registerTurnstileToken="",null==(e=b.value)||e.resetTurnstile()}))}}))},z=()=>{r.value.loginMode="phone",i("forgot-password-success")};s((()=>r.value.loginMode),(()=>{var e;const t=y.value;t&&t.restoreValidation(),w.value.loginTurnstileToken="",null==(e=y.value)||e.resetTurnstile()}));const P=o((()=>({width:"23rem",transition:"transform 0.6s, box-shadow 0.3s",transformStyle:"preserve-3d",transform:r.value.isFlipped?"rotateY(180deg)":"none",backgroundColor:"var(--creamy-white-1)",boxShadow:"0 8px 30px rgba(0, 0, 0, 0.12)",backdropFilter:"blur(5px)",border:"1px solid rgba(255, 255, 255, 0.2)",opacity:.8})));return t({switchToLogin:()=>{r.value.isFlipped=!1},switchToRegister:()=>{r.value.isFlipped=!0},resetAllForms:()=>{var e,t,a,l;c.value={phone:"",email:"",emailCode:"",password:""},u.value={username:"",phone:"",email:"",emailCode:"",password:"",reenteredPassword:"",job:""},d.value={sendCodeDisabled:!1,sendCodeText:"发送验证码",countdown:0},w.value={loginTurnstileToken:"",registerTurnstileToken:""},null==(e=y.value)||e.restoreValidation(),null==(t=b.value)||t.restoreValidation(),null==(a=y.value)||a.resetTurnstile(),null==(l=b.value)||l.resetTurnstile()}}),(e,t)=>(m(),f("div",uc,[p(ic,{particleCount:40}),h("div",dc,[p(g(He),{width:"200","preview-disabled":"",src:g(Os),loading:!0,"fallback-src":g(Os)},null,8,["src","fallback-src"])]),h("div",mc,[p(g(Re),{hoverable:"",style:k(P.value)},{default:v((()=>[$(p(Xs,{form:c.value,"onUpdate:form":t[0]||(t[0]=e=>c.value=e),"login-mode":r.value.loginMode,"onUpdate:loginMode":t[1]||(t[1]=e=>r.value.loginMode=e),"email-code-state":d.value,"is-valid-email":S.value,"is-turnstile-verified":M.value,loading:r.value.loginLoading,onLogin:_,onFlipCard:x,onShowForgotPassword:t[2]||(t[2]=e=>r.value.showForgotPassword=!0),onSendEmailCode:A,onTurnstileSuccess:C,onTurnstileError:L,ref_key:"loginFormRef",ref:y},null,8,["form","login-mode","email-code-state","is-valid-email","is-turnstile-verified","loading"]),[[D,!r.value.isFlipped]]),$(p(Zs,{form:u.value,"onUpdate:form":t[3]||(t[3]=e=>u.value=e),"email-code-state":d.value,"is-valid-email":T.value,"is-turnstile-verified":R.value,onRegister:I,onFlipCard:x,onSendEmailCode:A,onTurnstileSuccess:C,onTurnstileError:L,ref_key:"registerFormRef",ref:b},null,8,["form","email-code-state","is-valid-email","is-turnstile-verified"]),[[D,r.value.isFlipped]])])),_:1},8,["style"])]),h("div",vc,[p(Lo)]),p(g(cc),{show:r.value.showForgotPassword,"onUpdate:show":t[4]||(t[4]=e=>r.value.showForgotPassword=e),onSuccess:z},null,8,["show"])]))}}),[["__scopeId","data-v-5753cc41"]]),gc=Oa(i({__name:"CreatePrivilegeButton",emits:["click"],setup(e,{emit:t}){const a=t,l=n(!1),i=n(null),o=n(null),s=()=>{a("click")},c=()=>{l.value||(o.value&&(clearTimeout(o.value),o.value=null),l.value=!0,i.value=window.setTimeout((()=>{l.value=!1,v()}),1500))},u=()=>{l.value||v()},v=()=>{o.value&&(clearTimeout(o.value),o.value=null);const e=5e3+1e4*Math.random();o.value=window.setTimeout((()=>{p()}),e)},p=()=>{l.value?v():(l.value=!0,i.value=window.setTimeout((()=>{l.value=!1,v()}),1500))};return r((()=>{v()})),z((()=>{i.value&&(clearTimeout(i.value),i.value=null),o.value&&(clearTimeout(o.value),o.value=null)})),(e,t)=>(m(),d(g(gn),{size:36,color:"var(--blue)",onClick:s,class:y(["cursor-pointer create-privilege-button",{"is-rotating":l.value}]),ref:"createButtonRef",onMouseenter:c,onMouseleave:u},null,8,["class"]))}}),[["__scopeId","data-v-942c6528"]]);const hc=0,fc=1,wc={[hc]:"短信验证",[fc]:"二维码验证"};function yc(e){const t=e-Date.now();if(t<=0)return"已过期";if(t<6e4)return"即将过期";if(t<36e5)return`剩余${Math.floor(t/6e4)}分钟`;if(t<864e5)return`剩余${Math.floor(t/36e5)}小时`;if(t<2592e6)return`剩余${Math.floor(t/864e5)}天`;if(t<31104e6){return`剩余${Math.floor(t/2592e6)}个月`}return`剩余${Math.floor(t/31104e6)}年`}const bc={class:"article-header"},kc=["onClick"],xc={class:"flex-between-center"},Cc={class:"privilege-expire-time"},Lc={class:"article-content"},Sc={class:"privilege-description"},Tc={class:"infinite-load-info"},Mc=Oa(i({__name:"PrivilegeList",props:{searchCondition:{}},emits:["reset","startVerification"],setup(e,{expose:t,emit:a}){const l=e,i=a,u=Pe(),y=n([]),b=n(!1),x=n(!1),T=n(!0),M=n(void 0),R=n(),A=n(),{currentTheme:_}=function(){const e=n(da.value),t=[{name:aa.LIGHT,displayName:"浅色主题",description:"明亮清新的浅色界面",primaryColor:"#1890ff",isDark:!1},{name:aa.DARK,displayName:"深色主题",description:"护眼舒适的深色界面",primaryColor:"#177ddc",isDark:!0}],a=o((()=>e.value===aa.DARK)),l=o((()=>e.value===aa.LIGHT)),i=o((()=>t)),r=o((()=>t.find((t=>t.name===e.value)))),c=t=>{e.value=t,t!==da.value&&va()};return s(da,(t=>{e.value=t}),{immediate:!0}),{currentTheme:e,isDarkTheme:a,isLightTheme:l,availableThemes:i,currentThemeConfig:r,toggleTheme:()=>{const e=a.value?aa.LIGHT:aa.DARK;c(e)},setTheme:c,setDarkTheme:()=>{c(aa.DARK)},setLightTheme:()=>{c(aa.LIGHT)},setSystemTheme:()=>{if("undefined"!=typeof window&&window.matchMedia){const e=window.matchMedia("(prefers-color-scheme: dark)").matches;c(e?aa.DARK:aa.LIGHT)}},watchSystemTheme:()=>{if("undefined"==typeof window||!window.matchMedia)return()=>{};const e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{c(e.matches?aa.DARK:aa.LIGHT)};return e.addEventListener("change",t),()=>{e.removeEventListener("change",t)}}}}(),I=["rgba(255, 182, 193, 0.3)","rgba(173, 216, 230, 0.3)","rgba(144, 238, 144, 0.3)","rgba(255, 218, 185, 0.3)","rgba(221, 160, 221, 0.3)","rgba(255, 255, 224, 0.3)"],B=["rgba(139, 69, 19, 0.3)","rgba(25, 25, 112, 0.3)","rgba(0, 100, 0, 0.3)","rgba(128, 0, 128, 0.3)","rgba(165, 42, 42, 0.3)","rgba(184, 134, 11, 0.3)"],$=o((()=>_.value===aa.DARK)),D=(e,t)=>{const a=$.value?B:I;return a[t%a.length]},U=n(24),V=n(1),F=()=>{const e=window.innerWidth;let t=24,a=1;e>=2400?(t=3,a=8):e>=1800?(t=4,a=6):e>=1400?(t=6,a=4):e>=1e3?(t=8,a=3):e>=600?(t=12,a=2):(t=24,a=1),U.value=t,V.value=a},O={toggleTimeFormat:e=>{void 0===e.showExactExpireTime?e.showExactExpireTime=!0:e.showExactExpireTime=!e.showExactExpireTime}},H=e=>{i("startVerification",e)},j=async()=>{!T.value||b.value||x.value||await N(!0)},N=async(e=!1)=>{if(!(b.value||e&&x.value)){e?x.value=!0:(b.value=!0,y.value=[],M.value=void 0,T.value=!0);try{const t={...l.searchCondition.value,id:e?M.value:void 0,loadSize:20},a=await ys.search(t);if(a.success&&a.data){const t=a.data.map((e=>{return{...e,exactExpireTime:(t=e.expireTime,ro.toTimeString(t.toString(),"YYYY-MM-DD HH:mm:ss")),relativeExpireTime:yc(e.expireTime)};var t}));e?y.value.push(...t):y.value=t,t.length>0&&(M.value=t[t.length-1].id),T.value=20===t.length}else u.error("获取特权列表失败")}catch(t){u.error("搜索失败，请稍后重试")}finally{b.value=!1,x.value=!1}}};return r((async()=>{await c(),F(),window.addEventListener("resize",F)})),z((()=>{window.removeEventListener("resize",F)})),t({search:N,reset:()=>{y.value=[],M.value=void 0,T.value=!0,i("reset")},getLoadingState:()=>b.value||x.value}),(e,t)=>(m(),f("div",{class:"article-container",ref_key:"containerRef",ref:R},[p(g(Ce),{onLoad:j,distance:100,class:"infinite-scroll-container",ref_key:"scrollContainerRef",ref:A},{default:v((()=>[p(g(Ee),{gutter:20,style:{width:"100%","box-sizing":"border-box",margin:"0 auto",padding:"0 0.25rem",flex:"1","overflow-y":"auto"}},{default:v((()=>[(m(!0),f(L,null,S(y.value,((e,t)=>(m(),d(g(_e),{key:e.id,span:U.value},{default:v((()=>[p(g(Re),{class:"card-item cursor-pointer privilege-card compact-card",onClick:t=>H(e),"header-style":"padding-bottom:0.125rem;border-bottom: var(--border-1);",style:k({backgroundColor:D(e.id.toString(),t)})},{header:v((()=>[h("div",bc,[h("div",{class:"article-title",onClick:P((t=>H(e)),["stop"])},w(e.name),9,kc)])])),"header-extra":v((()=>[p(g(se),{round:"",size:32,src:e.icon||"/default-privilege-icon.png","object-fit":"cover",class:"article-avatar"},null,8,["src"])])),default:v((()=>[h("div",xc,[h("div",null,[p(g(Me),{type:0===e.verificationType?"info":"success",class:"card-tag"},{default:v((()=>{return[E(w((t=e.verificationType,wc[t]||"未知")),1)];var t})),_:2},1032,["type"])]),h("div",Cc,[p(g(Me),{type:"warning",size:"small",class:"time-clickable",onClick:P((t=>g(O).toggleTimeFormat(e)),["stop"])},{default:v((()=>[E(w(e.showExactExpireTime?e.exactExpireTime:e.relativeExpireTime),1)])),_:2},1032,["onClick"])])]),h("div",Lc,[p(g(ae),{style:{"padding-right":"0.5rem"}},{default:v((()=>[h("div",Sc,w(e.description),1)])),_:2},1024)])])),_:2},1032,["onClick","style"])])),_:2},1032,["span"])))),128))])),_:1}),h("div",Tc,[b.value?(m(),d(g(Le),{key:0,class:"display-flex"})):C("",!0),0!==y.value.length||b.value?C("",!0):(m(),d(g(Se),{key:1,description:"暂无特权数据"})),!T.value&&y.value.length>0?(m(),d(g(Se),{key:2,description:"没有更多特权了..."})):C("",!0)])])),_:1},512)],512))}}),[["__scopeId","data-v-9ef36929"]]),Rc={URL:"/core/user-privilege-verifications",startVerification:async e=>(await Ca.post(Rc.URL,e)).data,verificationStatus:async e=>(await Ca.get(`${Rc.URL}/${e}/status`)).data,submitVerificationContent:async(e,t)=>(await Ca.put(`${Rc.URL}/${e}/content`,t)).data,triggerStepThree:async e=>(await Ca.put(`${Rc.URL}/${e}/step-three`)).data,startVerificationTimer:async e=>(await Ca.post(`${Rc.URL}/${e}/timer`)).data,remainingTime:async e=>(await Ca.get(`${Rc.URL}/${e}/remaining-time`)).data},Ac=0,Ec=1,_c=2,Ic=3;const zc=1,Pc=2,Bc=3,$c={[zc]:"启动验证",[Pc]:"提交验证",[Bc]:"等待完成"},Dc={[zc]:"选择验证类型并启动验证流程",[Pc]:"根据验证类型提交相应的验证内容",[Bc]:"等待验证完成，获取特权"},Uc={class:"privilege-verification-modal"},Vc={class:"step-content"},Fc={key:0,class:"step-one"},Oc={class:"step-description"},Hc={class:"step-actions"},jc={key:1,class:"step-two"},Nc={class:"step-description"},qc={key:0,class:"remaining-time"},Yc={class:"verification-input"},Wc={class:"step-actions"},Jc={key:2,class:"step-three"},Kc={class:"verification-status"},Xc={key:0,class:"status-progress"},Gc={key:0,class:"remaining-time"},Zc={key:1,class:"status-success"},Qc={key:2,class:"status-failed"},eu={key:3,class:"status-timeout"},tu={class:"step-actions"},au=Oa(i({__name:"PrivilegeVerificationModal",props:{modelValue:{type:Boolean},privilege:{}},emits:["update:modelValue","success"],setup(e,{emit:t}){const a=e,l=t,i=function(){const e=Pe(),t=n(null),a=n(!1),l=n(!1),i=n(null),r=n(null),s=o((()=>{var e;return(null==(e=t.value)?void 0:e.currentStep)||1})),c=o((()=>{var e;return(null==(e=t.value)?void 0:e.status)||0})),u=o((()=>c.value===Ac)),d=o((()=>c.value===Ec)),m=o((()=>c.value===_c)),v=o((()=>c.value===Ic)),p=o((()=>{var e;return(null==(e=i.value)?void 0:e.remainingTime)||0})),g=o((()=>{var e;return(null==(e=i.value)?void 0:e.expired)||!1})),h=async a=>{try{const l=await Rc.verificationStatus(a);return l.success&&l.data?(t.value=l.data,!0):(e.error(l.message||"查询验证状态失败"),!1)}catch(l){return e.error("查询验证状态失败"),!1}},f=async()=>{if(!t.value)return!1;try{const a=await Rc.startVerificationTimer(t.value.id);return a.success&&a.data?(i.value=a.data,y(),!0):(e.error(a.message||"启动计时器失败"),!1)}catch(a){return!1}},w=async()=>{if(!t.value)return!1;try{const e=await Rc.remainingTime(t.value.id);return!(!e.success||!e.data||(i.value=e.data,0))}catch(e){return!1}},y=()=>{r.value&&clearInterval(r.value),r.value=window.setInterval((async()=>{if(t.value){if(await w(),g.value)return window.clearInterval(r.value),r.value=null,void e.warning("验证流程已过期");await h(t.value.id),u.value||(window.clearInterval(r.value),r.value=null)}}),1e3)},b=()=>{r.value&&(window.clearInterval(r.value),r.value=null)};return z((()=>{b()})),{verificationData:t,isLoading:a,isSubmitting:l,timeInfo:i,currentStep:s,verificationStatus:c,isInProgress:u,isCompleted:d,isFailed:m,isTimeout:v,remainingTime:p,isExpired:g,startVerification:async l=>{try{a.value=!0;const n=await Rc.startVerification(l);return n.success&&n.data?(t.value=n.data,e.success("验证流程已启动"),await f(),!0):(e.error(n.message||"启动验证流程失败"),!1)}catch(n){return e.error("启动验证流程失败"),!1}finally{a.value=!1}},queryVerificationStatus:h,submitVerificationContent:async a=>{if(!t.value)return e.error("验证流程不存在"),!1;try{l.value=!0;const n={content:a},i=await Rc.submitVerificationContent(t.value.id,n);return i.success&&i.data?(e.success("验证内容提交成功"),await h(t.value.id),!0):(e.error(i.message||"提交验证内容失败"),!1)}catch(n){return e.error("提交验证内容失败"),!1}finally{l.value=!1}},startTimer:f,getRemainingTime:w,stopTimer:b,resetVerification:()=>{t.value=null,i.value=null,a.value=!1,l.value=!1,b()},formatRemainingTime:e=>{if(e<=0)return"00:00";const t=Math.floor(e/6e4),a=Math.floor(e%6e4/1e3);return`${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}}}(),r=o({get:()=>a.modelValue,set:e=>l("update:modelValue",e)}),c=n(null),u=n(""),y=o((()=>{var e;return(null==(e=a.privilege)?void 0:e.name)||""})),b=[{value:1,title:$c[1],description:Dc[1]},{value:2,title:$c[2],description:Dc[2]},{value:3,title:$c[3],description:Dc[3]}],k=()=>{if(!i.verificationData.value)return"";const e=i.verificationData.value.verificationType,t=wc[e];return 0===e?`请输入通过${t}收到的验证码或相关信息`:`请输入通过${t}获取的验证信息`},x=()=>{if(!i.verificationData.value)return"请输入验证内容";return 0===i.verificationData.value.verificationType?"请输入短信验证码":"请输入二维码验证信息"},T=async()=>{if(!a.privilege||null===c.value)return;await i.startVerification({privilegeId:a.privilege.id,verificationType:c.value})},M=async()=>{if(!u.value.trim())return;await i.submitVerificationContent(u.value.trim())&&(u.value="")},R=()=>{i.isCompleted.value&&l("success"),A(),r.value=!1},A=()=>{c.value=null,u.value="",i.resetVerification()};return s(r,(e=>{e&&a.privilege?c.value=a.privilege.verificationType:e||A()})),(e,t)=>(m(),d(g(re),{show:r.value,"onUpdate:show":t[2]||(t[2]=e=>r.value=e),preset:"dialog",title:"特权验证","mask-closable":!1,closable:!g(i).isLoading&&!g(i).isSubmitting,style:{width:"600px"},onClose:R},{default:v((()=>[h("div",Uc,[p(g(je),{current:g(i).currentStep.value,status:i.isCompleted.value?"finish":i.isFailed.value||i.isTimeout.value?"error":i.isInProgress.value?"process":"wait",class:"verification-steps"},{default:v((()=>[(m(),f(L,null,S(b,(e=>p(g(Ne),{key:e.value,title:e.title,description:e.description},null,8,["title","description"]))),64))])),_:1},8,["current","status"]),h("div",Vc,[1===g(i).currentStep.value?(m(),f("div",Fc,[t[7]||(t[7]=h("div",{class:"step-title"},"选择验证类型",-1)),h("div",Oc,"请选择适合的验证方式来获取特权："+w(y.value),1),p(g(ve),{value:c.value,"onUpdate:value":t[0]||(t[0]=e=>c.value=e),class:"verification-type-group"},{default:v((()=>[p(g(qe),{direction:"vertical"},{default:v((()=>[p(g($e),{value:0},{default:v((()=>t[3]||(t[3]=[h("div",{class:"verification-option"},[h("div",{class:"option-title"},"短信验证"),h("div",{class:"option-description"},"通过短信接收验证码进行验证")],-1)]))),_:1}),p(g($e),{value:1},{default:v((()=>t[4]||(t[4]=[h("div",{class:"verification-option"},[h("div",{class:"option-title"},"二维码验证"),h("div",{class:"option-description"},"通过扫描二维码进行验证")],-1)]))),_:1})])),_:1})])),_:1},8,["value"]),h("div",Hc,[p(g(ne),{onClick:R,disabled:g(i).isLoading.value},{default:v((()=>t[5]||(t[5]=[E(" 取消 ")]))),_:1},8,["disabled"]),p(g(ne),{type:"primary",onClick:T,loading:g(i).isLoading.value,disabled:null===c.value},{default:v((()=>t[6]||(t[6]=[E(" 开始验证 ")]))),_:1},8,["loading","disabled"])])])):2===g(i).currentStep.value?(m(),f("div",jc,[t[10]||(t[10]=h("div",{class:"step-title"},"提交验证内容",-1)),h("div",Nc,w(k()),1),g(i).remainingTime.value>0?(m(),f("div",qc,[p(g(Me),{type:"warning",size:"large"},{default:v((()=>[E(" 剩余时间："+w(g(i).formatRemainingTime(g(i).remainingTime.value)),1)])),_:1})])):C("",!0),h("div",Yc,[p(g(oe),{value:u.value,"onUpdate:value":t[1]||(t[1]=e=>u.value=e),type:"textarea",placeholder:x(),rows:4,disabled:g(i).isSubmitting.value,"show-count":"",maxlength:500},null,8,["value","placeholder","disabled"])]),h("div",Wc,[p(g(ne),{onClick:R,disabled:g(i).isSubmitting.value},{default:v((()=>t[8]||(t[8]=[E(" 取消 ")]))),_:1},8,["disabled"]),p(g(ne),{type:"primary",onClick:M,loading:g(i).isSubmitting.value,disabled:!u.value.trim()},{default:v((()=>t[9]||(t[9]=[E(" 提交验证 ")]))),_:1},8,["loading","disabled"])])])):3===g(i).currentStep.value?(m(),f("div",Jc,[t[17]||(t[17]=h("div",{class:"step-title"},"等待验证完成",-1)),t[18]||(t[18]=h("div",{class:"step-description"},"验证内容已提交，请等待验证完成",-1)),h("div",Kc,[g(i).isInProgress.value?(m(),f("div",Xc,[p(g(Le),{size:"large"}),t[11]||(t[11]=h("div",{class:"status-text"},"验证进行中...",-1)),g(i).remainingTime.value>0?(m(),f("div",Gc," 剩余时间："+w(g(i).formatRemainingTime(g(i).remainingTime.value)),1)):C("",!0)])):g(i).isCompleted.value?(m(),f("div",Zc,t[12]||(t[12]=[h("div",{class:"status-icon success-icon"},"✓",-1),h("div",{class:"status-text"},"验证成功！",-1),h("div",{class:"status-description"},"特权已成功获取",-1)]))):g(i).isFailed.value?(m(),f("div",Qc,t[13]||(t[13]=[h("div",{class:"status-icon failed-icon"},"✗",-1),h("div",{class:"status-text"},"验证失败",-1),h("div",{class:"status-description"},"请重新尝试验证",-1)]))):g(i).isTimeout.value?(m(),f("div",eu,t[14]||(t[14]=[h("div",{class:"status-icon timeout-icon"},"⏰",-1),h("div",{class:"status-text"},"验证超时",-1),h("div",{class:"status-description"},"验证时间已过期",-1)]))):C("",!0)]),h("div",tu,[g(i).isCompleted.value||g(i).isFailed.value||g(i).isTimeout.value?(m(),d(g(ne),{key:0,type:"primary",onClick:R},{default:v((()=>t[15]||(t[15]=[E(" 确定 ")]))),_:1})):(m(),d(g(ne),{key:1,onClick:R},{default:v((()=>t[16]||(t[16]=[E(" 取消 ")]))),_:1}))])])):C("",!0)])])])),_:1},8,["show","closable"]))}}),[["__scopeId","data-v-f56bdba0"]]),lu="privilege_search_condition";const nu={class:"home-layout"},iu={class:"home-layout-top"},ou={class:"middle-controls-container"},ru={class:"tag-bar-wrapper"},su={class:"home-layout-content"},cu=Oa(i({__name:"Privilege",props:{initialSearchCondition:{default:void 0},autoLoad:{type:Boolean,default:!0}},emits:["search-condition-change","privilege-create-success"],setup(e,{expose:t,emit:a}){const l=W((()=>Ar((()=>Promise.resolve().then((()=>As))),void 0))),i=e,u=a,d=function(){const e=Pe(),t=n(!1),a=n(!1),l=n(null),i=n({searchKey:"",owner:!1,interaction:!1,favorite:!1,tag:""}),r=o((()=>""!==i.value.searchKey.trim()||i.value.owner||i.value.interaction||i.value.favorite||!!i.value.tag&&""!==i.value.tag.trim())),s=async(n,i=!1)=>{if(n){l.value&&l.value.abort(),l.value=new AbortController,t.value=!0,a.value=!0;try{await n.search(i)}catch(o){o instanceof Error&&"AbortError"!==o.name&&e.error("搜索失败，请稍后重试")}finally{t.value=!1,a.value=!1,l.value=null}}},c=()=>{ia.set(lu,i.value)};return{isLoading:t,isSearching:a,searchCondition:i,hasSearchCondition:r,search:s,handleTagSelected:async(e,t)=>{i.value.tag=e,c(),await s(t)},saveSearchCondition:c,loadSearchCondition:()=>{const e=ia.get(lu);e&&(i.value={...i.value,...e})},cleanup:()=>{l.value&&(l.value.abort(),l.value=null)}}}(),v=function(){const e=n(null),t=n(null);return{privilegeModalRef:e,privilegeListRef:t,initializeState:()=>{},openCreatePrivilegeDialog:()=>{e.value&&e.value.open()},resetPrivilegeList:()=>{t.value&&t.value.reset()},cleanup:()=>{}}}(),w=n(null),y=n(null),b=n(!1),k=n(!1),x=n(null);s(w,(e=>{v.privilegeModalRef.value=e}),{immediate:!0}),s(y,(e=>{v.privilegeListRef.value=e}),{immediate:!0}),r((async()=>{v.initializeState(),i.initialSearchCondition?(d.searchCondition.value={...d.searchCondition.value,...i.initialSearchCondition},d.saveSearchCondition()):d.loadSearchCondition(),await c(),i.autoLoad&&setTimeout((async()=>{await C()}),50)})),z((()=>{d.cleanup(),v.cleanup()}));const C=async(e=!1)=>{ta.debug("执行特权搜索操作:",{privilegeListRef:v.privilegeListRef.value,loadMore:e}),await c(),d.search(v.privilegeListRef.value,e),u("search-condition-change",d.searchCondition.value)},L=async()=>{v.resetPrivilegeList(),await C(),u("privilege-create-success")},S=e=>{x.value=e,k.value=!0},T=async()=>{v.resetPrivilegeList(),await C(),x.value=null};return t({performSearch:async(e=!1)=>{await C(e)},resetPrivilegeList:()=>{v.resetPrivilegeList()},getCurrentSearchCondition:()=>d.searchCondition.value}),(e,t)=>(m(),f("div",nu,[h("div",iu,[h("div",ou,[p(us,{modelValue:g(d).searchCondition.value,"onUpdate:modelValue":t[0]||(t[0]=e=>g(d).searchCondition.value=e),placeholder:"搜索特权内容",onSearch:C},null,8,["modelValue"]),p(gc,{onClick:g(v).openCreatePrivilegeDialog},null,8,["onClick"])]),p(g(l),{ref_key:"privilegeModalRef",ref:w,modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=e=>b.value=e),onSuccess:L},null,8,["modelValue"]),p(au,{modelValue:k.value,"onUpdate:modelValue":t[2]||(t[2]=e=>k.value=e),privilege:x.value,onSuccess:T},null,8,["modelValue","privilege"]),p(Uo)]),h("div",ru,[p(ms,{modelValue:g(d).searchCondition.value.tag,"onUpdate:modelValue":t[3]||(t[3]=e=>g(d).searchCondition.value.tag=e),onTagSelected:t[4]||(t[4]=e=>g(d).handleTagSelected(e,g(v).privilegeListRef.value))},null,8,["modelValue"])]),h("div",su,[p(Mc,{"search-condition":g(d).searchCondition,ref_key:"privilegeListRef",ref:y,onReset:g(v).resetPrivilegeList,onStartVerification:S},null,8,["search-condition","onReset"])])]))}}),[["__scopeId","data-v-6ce31cce"]]),uu=Je({history:Ke("/"),routes:[{path:"/login",name:"Login",component:pc,meta:{requiresAuth:!1}},{path:"/",name:"Home",component:Fs,meta:{requiresAuth:!0}},{path:"/article/:articleId/:commentId?",name:"Article",component:Mr,meta:{requiresAuth:!0}},{path:"/privilege",name:"Privilege",component:cu,meta:{requiresAuth:!0}}]}),du="Shenmo";uu.beforeEach(((e,t,a)=>{var l;document.title=`${du} - ${e.name}`;const n=function(e){const t=document.cookie.split("; ");for(const a of t){const t=a.split("=");if(t[0]===e)return t[1]}return null}("wentk"),i=Wn(),o=Jn();if(i.setId(""),o.setId(""),(null==(l=e.meta)?void 0:l.requiresAuth)&&!n)a({name:"Login"});else if("Login"===e.name&&n)a({name:"Home"});else{if("Article"===e.name){const{articleId:t,commentId:a}=e.params;i.setId(String(t)),null!=a&&o.setId(String(a)),Yn.title(i.getId).then((e=>{document.title=`${du} - ${e.data}`}))}a()}}));H(ha).use(uu).use(l()).mount("#app");export{Oa as _,ys as e};
