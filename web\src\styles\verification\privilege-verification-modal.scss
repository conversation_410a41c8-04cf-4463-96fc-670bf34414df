/**
 * 特权验证流程弹框样式
 */

.privilege-verification-modal {
  padding: 1rem 0;

  .verification-steps {
    margin-bottom: 2rem;
  }

  .step-content {
    min-height: 300px;

    .step-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--text-color-1);
      margin-bottom: 0.5rem;
    }

    .step-description {
      color: var(--text-color-2);
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .step-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid var(--border-color);
    }
  }

  // 步骤一样式
  .step-one {
    .verification-type-group {
      margin-bottom: 1.5rem;

      .verification-option {
        .option-title {
          font-weight: 500;
          color: var(--text-color-1);
          margin-bottom: 0.25rem;
        }

        .option-description {
          font-size: 0.875rem;
          color: var(--text-color-3);
        }
      }
    }
  }

  // 步骤二样式
  .step-two {
    .remaining-time {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;
    }

    .verification-input {
      margin-bottom: 1rem;
    }
  }

  // 步骤三样式
  .step-three {
    .verification-status {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 200px;
      text-align: center;

      .status-progress,
      .status-success,
      .status-failed,
      .status-timeout {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
      }

      .status-icon {
        font-size: 3rem;
        font-weight: bold;
        border-radius: 50%;
        width: 4rem;
        height: 4rem;
        display: flex;
        align-items: center;
        justify-content: center;

        &.success-icon {
          color: #18a058;
          background-color: rgba(24, 160, 88, 0.1);
        }

        &.failed-icon {
          color: #d03050;
          background-color: rgba(208, 48, 80, 0.1);
        }

        &.timeout-icon {
          color: #f0a020;
          background-color: rgba(240, 160, 32, 0.1);
        }
      }

      .status-text {
        font-size: 1.125rem;
        font-weight: 500;
        color: var(--text-color-1);
      }

      .status-description {
        font-size: 0.875rem;
        color: var(--text-color-2);
      }

      .remaining-time {
        font-size: 0.875rem;
        color: var(--text-color-3);
        margin-top: 0.5rem;
      }
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .privilege-verification-modal {
    .step-content {
      min-height: 250px;
    }

    .step-actions {
      flex-direction: column-reverse;
      gap: 0.5rem;

      .n-button {
        width: 100%;
      }
    }
  }
}

// 深色主题适配
[data-theme='dark'] {
  .privilege-verification-modal {
    .step-title {
      color: var(--text-color-1);
    }

    .step-description {
      color: var(--text-color-2);
    }

    .verification-option {
      .option-title {
        color: var(--text-color-1);
      }

      .option-description {
        color: var(--text-color-3);
      }
    }

    .status-text {
      color: var(--text-color-1);
    }

    .status-description {
      color: var(--text-color-2);
    }
  }
}
