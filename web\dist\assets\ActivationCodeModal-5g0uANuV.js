import{d as s,t as o,r,Y as e,Z as i,_ as t,a0 as a,$ as p,u as m,a8 as l,q as d}from"./@vue-C18CzuYV.js";import{a as j}from"./vue-router-CM3lNrYT.js";import{e as n,_ as u}from"./index-VcQTjGER.js";import{M as c,j as X,B as v,k as f}from"./naive-ui-uw0PP3R0.js";import"./pinia-C1NaKXqp.js";import"./axios-CF6-kBsv.js";import"./vite-plugin-node-polyfills-CgB-Lgxu.js";import"./@tiptap-BlbpWldy.js";import"./prosemirror-dropcursor-b1zulL1f.js";import"./prosemirror-state-Dg8eoqF5.js";import"./prosemirror-model-BwtArlLQ.js";import"./orderedmap-6uBVSRPO.js";import"./prosemirror-transform-2YHSeD6B.js";import"./prosemirror-view-D1p6KQzm.js";import"./prosemirror-gapcursor-De4VkbqI.js";import"./prosemirror-keymap-CxzETJ23.js";import"./w3c-keyname-DcELQ0J3.js";import"./prosemirror-history-B0l72feN.js";import"./rope-sequence-DIBo4VXF.js";import"./linkifyjs-CgqKPF1I.js";import"./tippy.js-Cq-jft6S.js";import"./@popperjs-B5AGR5A_.js";import"./prosemirror-commands-jNaZT4ky.js";import"./prosemirror-schema-list-DJ0wlwD0.js";import"./tiptap-markdown-C1mL59Et.js";import"./prosemirror-markdown-BKJFDS0M.js";import"./markdown-it-DV9r8h9J.js";import"./mdurl-DbZ9s47_.js";import"./uc.micro-CRGj88R_.js";import"./entities-D_unbCD7.js";import"./linkify-it-DVBmImI5.js";import"./punycode.js-H98b6B6Y.js";import"./markdown-it-task-lists-Dj-XbLVy.js";import"./highlight.js-O_OqoeFy.js";import"./smooth-scroll-into-view-if-needed-B9OyoO8F.js";import"./scroll-into-view-if-needed-CoacbxIo.js";import"./compute-scroll-into-view-Cfyw3hb3.js";import"./lowlight-CRhSxQ06.js";import"./sockjs-client-CnUIS0PG.js";import"./url-parse-BJt2elfP.js";import"./requires-port-CgMabaHb.js";import"./querystringify-B2QvdZsH.js";import"./inherits-BfZYsuMB.js";import"./@stomp-ba8ZO4qr.js";import"./seemly-Cdi3gWV3.js";import"./vueuc-BKIYztcR.js";import"./evtd-hWw0KU7y.js";import"./@css-render-DcoOFqSU.js";import"./vooks-n2t0Q59N.js";import"./vdirs-Bvq7nEML.js";import"./@juggle-BnTvdTVm.js";import"./css-render-7x70jhNC.js";import"./@emotion-DFFAhID7.js";import"./lodash-es-BpE61GNB.js";import"./treemate-D3ikBJ7G.js";import"./date-fns-B2WPOLoy.js";import"./date-fns-tz-DYDqAHgp.js";import"./async-validator-Bed4cEOw.js";const g={class:"activation-code-modal"},h={class:"activation-code-input"},y={class:"modal-actions"},w=u(s({__name:"ActivationCodeModal",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(s,{expose:u,emit:w}){const k=s,A=w,Z=j(),_=c(),b=o({get:()=>k.modelValue,set:s=>A("update:modelValue",s)}),x=r(""),q=r(!1),C=o((()=>/^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(x.value))),V=s=>{const o=s.replace(/[^A-Z0-9]/g,"").toUpperCase();let r="";for(let e=0;e<o.length;e++)e>0&&e%4==0&&(r+="-"),r+=o[e];x.value=r,24===o.length&&U()},U=async()=>{if(C.value){q.value=!0;try{const s=await n.activate({code:x.value});s.success&&s.data?(_.success("激活成功！"),b.value=!1,A("success"),await Z.push("/privilege")):_.error("激活失败，请检查激活码是否正确")}catch(s){_.error("激活失败，请稍后重试")}finally{q.value=!1}}else _.warning("请输入正确格式的激活码")},B=()=>{b.value=!1,x.value=""};return u({reset:()=>{x.value="",q.value=!1}}),(s,o)=>(i(),e(m(f),{show:b.value,"onUpdate:show":o[1]||(o[1]=s=>b.value=s),preset:"dialog",title:"输入激活码","mask-closable":!1},{default:t((()=>[a("div",g,[a("div",h,[p(m(X),{value:x.value,"onUpdate:value":o[0]||(o[0]=s=>x.value=s),placeholder:"请输入激活码（XXXX-XXXX-XXXX-XXXX-XXXX-XXXX）",maxlength:29,loading:q.value,onInput:V,onKeyup:l(U,["enter"])},null,8,["value","loading"]),o[2]||(o[2]=a("div",{class:"code-format-hint"},"格式：XXXX-XXXX-XXXX-XXXX-XXXX-XXXX（6组，每组4个字符）",-1))]),a("div",y,[p(m(v),{onClick:B,disabled:q.value},{default:t((()=>o[3]||(o[3]=[d("取消")]))),_:1},8,["disabled"]),p(m(v),{type:"primary",onClick:U,loading:q.value,disabled:!C.value},{default:t((()=>o[4]||(o[4]=[d(" 激活 ")]))),_:1},8,["loading","disabled"])])])])),_:1},8,["show"]))}}),[["__scopeId","data-v-a649c8ee"]]);export{w as default};
